<body>
<p>Bukkit, the plugin development framework.</p>

<p>
    The documentation is for developing plugins and is split into the
    respective packages for each subject matter. This documentation does not
    cover running a server, contributing code back to the project, or setting
    up a workspace. Working knowledge of the Java language is a prerequisite
    for developing plugins.
</p>
<p>
    For basic plugin development, see the {@link org.bukkit.plugin plugin
    package}. It covers the basic requirements of a plugin jar.
</p>
<p>
    For handling events and triggered code, see the {@link org.bukkit.event
    event package}.
</p>
</body>
