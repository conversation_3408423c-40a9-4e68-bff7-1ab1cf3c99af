<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.bukkit</groupId>
    <artifactId>bukkit</artifactId>
    <version>1.7.9-R0.3-SNAPSHOT</version>
    <name>Bukkit</name>
    <url>http://www.bukkit.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <scm>
        <developerConnection>scm:git:**************:Bukkit/Bukkit.git</developerConnection>
        <connection>scm:git:git://github.com/Bukkit/Bukkit.git</connection>
        <url>https://github.com/Bukkit/Bukkit/tree/master/</url>
    </scm>

    <ciManagement>
        <system>jenkins</system>
        <url>http://ci.bukkit.org</url>
    </ciManagement>

    <distributionManagement>
        <site>
            <id>jd.bukkit.org</id>
            <url>file:///home/<USER>/public_html/</url>
        </site>
        <repository>
            <id>repobo-rel</id>
            <name>repo.bukkit.org Releases</name>
            <url>http://repo.bukkit.org/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>repobo-snap</id>
            <name>repo.bukkit.org Snapshots</name>
            <url>http://repo.bukkit.org/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>1.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.0-beta-3</version>
                <configuration>
                    <reportPlugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>jxr-maven-plugin</artifactId>
                            <version>2.0-beta-1</version>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-javadoc-plugin</artifactId>
                            <version>2.7</version>
                            <configuration>
                                <linksource>true</linksource>
                            </configuration>
                            <reportSets>
                                <reportSet>
                                    <reports>
                                        <report>javadoc</report>
                                    </reports>
                                </reportSet>
                            </reportSets>
                        </plugin>
                    </reportPlugins>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.9</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.avaje</groupId>
            <artifactId>ebean</artifactId>
            <version>2.7.3</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>10.0.1</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.3</version>
        </dependency>

        <!-- testing -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
