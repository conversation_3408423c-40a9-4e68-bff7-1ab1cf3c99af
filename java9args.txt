--illegal-access=warn
-Djava.security.manager=allow
-Dfile.encoding=UTF-8
-Dcrucible.weAreJava9=true
--add-opens
java.base/jdk.internal.loader=ALL-UNNAMED
--add-opens
java.base/java.net=ALL-UNNAMED
--add-opens
java.base/java.nio=ALL-UNNAMED
--add-opens
java.base/java.io=ALL-UNNAMED
--add-opens
java.base/java.lang=ALL-UNNAMED
--add-opens
java.base/java.lang.reflect=ALL-UNNAMED
--add-opens
java.base/java.text=ALL-UNNAMED
--add-opens
java.base/java.util=ALL-UNNAMED
--add-opens
java.base/jdk.internal.reflect=ALL-UNNAMED
--add-opens
java.base/sun.nio.ch=ALL-UNNAMED
--add-opens
jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED,java.naming
--add-opens
java.desktop/sun.awt=ALL-UNNAMED
--add-opens
java.desktop/sun.awt.image=ALL-UNNAMED
--add-opens
java.desktop/com.sun.imageio.plugins.png=ALL-UNNAMED
--add-modules jdk.dynalink
--add-opens
jdk.dynalink/jdk.dynalink.beans=ALL-UNNAMED
--add-modules java.sql.rowset
--add-opens
java.sql.rowset/javax.sql.rowset.serial=ALL-UNNAMED