--- ../src-base/minecraft/org/bukkit/Server.java
+++ ../src-work/minecraft/org/bukkit/Server.java
@@ -12,11 +12,17 @@
 import java.util.UUID;
 import java.util.logging.Logger;
 
+import net.md_5.bungee.api.chat.BaseComponent;
 import org.bukkit.Warning.WarningState;
+import org.bukkit.boss.BarColor;
+import org.bukkit.boss.BarFlag;
+import org.bukkit.boss.BarStyle;
+import org.bukkit.boss.BossBar;
 import org.bukkit.command.CommandException;
 import org.bukkit.command.CommandSender;
 import org.bukkit.command.ConsoleCommandSender;
 import org.bukkit.command.PluginCommand;
+import org.bukkit.configuration.file.YamlConfiguration;
 import org.bukkit.entity.Entity;
 import org.bukkit.entity.Player;
 import org.bukkit.event.inventory.InventoryType;
@@ -916,4 +922,32 @@
      */
     @Deprecated
     UnsafeValues getUnsafe();
+
+    public Spigot spigot();
+
+    public static class Spigot {
+        public YamlConfiguration getConfig() {
+            throw new UnsupportedOperationException("Not supported yet.");
+        }
+
+        public void broadcast(BaseComponent component) {
+            throw new UnsupportedOperationException("Not supported yet.");
+        }
+
+        public /* varargs */ void broadcast(BaseComponent ... components) {
+            throw new UnsupportedOperationException("Not supported yet.");
+        }
+    }
+
+    /**
+     * Creates a boss bar instance to display to players. The progress
+     * defaults to 1.0
+     *
+     * @param title the title of the boss bar
+     * @param color the color of the boss bar
+     * @param style the style of the boss bar
+     * @param flags an optional list of flags to set on the boss bar
+     * @return the created boss bar
+     */
+    BossBar createBossBar(String title, BarColor color, BarStyle style, BarFlag... flags);
 }
