--- ../src-base/minecraft/org/bukkit/entity/Entity.java
+++ ../src-work/minecraft/org/bukkit/entity/Entity.java
@@ -4,6 +4,7 @@
 import org.bukkit.EntityEffect;
 import org.bukkit.Server;
 import org.bukkit.World;
+import org.bukkit.command.CommandSender;
 import org.bukkit.event.entity.EntityDamageEvent;
 import org.bukkit.metadata.Metadatable;
 import org.bukkit.util.Vector;
@@ -15,7 +16,7 @@
 /**
  * Represents a base entity in the world
  */
-public interface Entity extends Metadatable {
+public interface Entity extends Metadatable, CommandSender {
 
     /**
      * Gets the entity's current position
@@ -291,4 +292,22 @@
      * @return The current vehicle.
      */
     public Entity getVehicle();
+
+    // Spigot Start
+    public class Spigot
+    {
+
+        /**
+         * Returns whether this entity is invulnerable.
+         *         
+        * @return True if the entity is invulnerable.
+         */
+        public boolean isInvulnerable()
+        {
+            throw new UnsupportedOperationException( "Not supported yet." );
+        }
+    }
+
+    Spigot spigot();
+    // Spigot End
 }
