--- ../src-base/minecraft/org/bukkit/entity/Arrow.java
+++ ../src-work/minecraft/org/bukkit/entity/Arrow.java
@@ -39,4 +39,22 @@
      * @param critical whether or not it should be critical
      */
     public void setCritical(boolean critical);
+
+    // Spigot start
+    public class Spigot extends Entity.Spigot
+    {
+
+        public double getDamage()
+        {
+            throw new UnsupportedOperationException( "Not supported yet." );
+        }
+
+        public void setDamage(double damage)
+        {
+            throw new UnsupportedOperationException( "Not supported yet." );
+        }
+    }
+
+    Spigot spigot();
+    // Spigot end
 }
