--- ../src-base/minecraft/org/bukkit/plugin/messaging/StandardMessenger.java
+++ ../src-work/minecraft/org/bukkit/plugin/messaging/StandardMessenger.java
@@ -421,7 +421,15 @@
         Set<PluginMessageListenerRegistration> registrations = getIncomingChannelRegistrations(channel);
 
         for (PluginMessageListenerRegistration registration : registrations) {
-            registration.getListener().onPluginMessageReceived(channel, source, message);
+            // Spigot Start
+            try
+            {
+                registration.getListener().onPluginMessageReceived( channel, source, message );
+            } catch ( Throwable t )
+            {
+                org.bukkit.Bukkit.getLogger().log( java.util.logging.Level.WARNING, "Could not pass incoming plugin message to " + registration.getPlugin(), t );
+            }
+            // Spigot End
         }
     }
 
