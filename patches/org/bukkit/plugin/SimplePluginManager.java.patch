--- ../src-base/minecraft/org/bukkit/plugin/SimplePluginManager.java
+++ ../src-work/minecraft/org/bukkit/plugin/SimplePluginManager.java
@@ -40,6 +40,7 @@
 public final class SimplePluginManager implements PluginManager {
     private final Server server;
     private final Map<Pattern, PluginLoader> fileAssociations = new HashMap<Pattern, PluginLoader>();
+    private final Set<PluginLoader> loaders = new HashSet<>(); // Crucible - used to look up plugins by class name
     private final List<Plugin> plugins = new ArrayList<Plugin>();
     private final Map<String, Plugin> lookupNames = new HashMap<String, Plugin>();
     private static File updateDirectory = null;
@@ -86,6 +87,7 @@
         }
 
         Pattern[] patterns = instance.getPluginFileFilters();
+        loaders.add(instance); // Crucible
 
         synchronized (this) {
             for (Pattern pattern : patterns) {
@@ -132,7 +134,9 @@
             try {
                 description = loader.getPluginDescription(file);
                 String name = description.getName();
-                if (name.equalsIgnoreCase("bukkit") || name.equalsIgnoreCase("minecraft") || name.equalsIgnoreCase("mojang")) {
+                if (name.equalsIgnoreCase("bukkit") || name.equalsIgnoreCase("minecraft") || name.equalsIgnoreCase("mojang")
+                        // Cauldron - Add more restricted names
+                        || name.equalsIgnoreCase("spigot") || name.equalsIgnoreCase("forge") || name.equalsIgnoreCase("cauldron") || name.equalsIgnoreCase("mcpc") || name.equalsIgnoreCase("kcauldron") || name.equalsIgnoreCase("thermos")) {
                     server.getLogger().log(Level.SEVERE, "Could not load '" + file.getPath() + "' in folder '" + directory.getPath() + "': Restricted Name");
                     continue;
                 } else if (description.rawName.indexOf(' ') != -1) {
@@ -188,6 +192,9 @@
             }
         }
 
+        // Cauldron - fill names for Cauldron-provided dependencies
+        loadedPlugins.addAll(ImmutableSet.of("Cauldron", "Forge", "MCPC", "MCPC+", "KCauldron", "Thermos"));
+
         while (!plugins.isEmpty()) {
             boolean missingDependency = true;
             Iterator<String> pluginIterator = plugins.keySet().iterator();
@@ -555,7 +562,8 @@
             throw new IllegalPluginAccessException("Plugin attempted to register " + event + " while not enabled");
         }
 
-        if (useTimings) {
+        executor = new co.aikar.timings.TimedEventExecutor(executor, plugin, null, event); // Paper
+        if (false) { // Spigot - RL handles useTimings check now // Paper
             getEventListeners(event).register(new TimedRegisteredListener(listener, executor, priority, plugin, ignoreCancelled));
         } else {
             getEventListeners(event).register(new RegisteredListener(listener, executor, priority, plugin, ignoreCancelled));
@@ -716,7 +724,7 @@
     }
 
     public boolean useTimings() {
-        return useTimings;
+        return co.aikar.timings.Timings.isTimingsEnabled(); // Spigot
     }
 
     /**
@@ -725,6 +733,23 @@
      * @param use True if per event timing code should be used
      */
     public void useTimings(boolean use) {
-        useTimings = use;
+        co.aikar.timings.Timings.setTimingsEnabled(use); // Paper
     }
+
+    //Crucible start
+
+    //Add a way to inject a mod plugin into the loaded plugins list.
+    public synchronized Plugin injectModPlugin(Plugin plugin) {
+        if (plugin != null && plugin.getDescription() != null) {
+            plugins.add(plugin);
+            lookupNames.put(plugin.getDescription().getName(), plugin);
+        }
+        return plugin;
+    }
+
+    public Set<PluginLoader> getLoaders() {
+        return loaders;
+    }
+
+    //Crucible end
 }
