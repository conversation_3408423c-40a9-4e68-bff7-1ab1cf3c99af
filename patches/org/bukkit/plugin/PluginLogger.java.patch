--- ../src-base/minecraft/org/bukkit/plugin/PluginLogger.java
+++ ../src-work/minecraft/org/bukkit/plugin/PluginLogger.java
@@ -22,14 +22,20 @@
     public PluginLogger(Plugin context) {
         super(context.getClass().getCanonicalName(), null);
         String prefix = context.getDescription().getPrefix();
-        pluginName = prefix != null ? new StringBuilder().append("[").append(prefix).append("] ").toString() : "[" + context.getDescription().getName() + "] ";
+        // Crucible start - give plugins a proper prefix for log4j
+        //pluginName = prefix != null ? new StringBuilder().append("[").append(prefix).append("] ").toString() : "[" + context.getDescription().getName() + "] ";
+        pluginName = prefix != null ? prefix : context.getDescription().getName();
+        // Crucible end
         setParent(context.getServer().getLogger());
         setLevel(Level.ALL);
     }
 
     @Override
     public void log(LogRecord logRecord) {
-        logRecord.setMessage(pluginName + logRecord.getMessage());
+        // Crucible start - fix log4j prefix
+        //logRecord.setMessage(pluginName + logRecord.getMessage());
+        logRecord.setLoggerName(pluginName);
+        // Crucible end
         super.log(logRecord);
     }
 
