--- ../src-base/minecraft/org/bukkit/event/entity/EntityDamageByBlockEvent.java
+++ ../src-work/minecraft/org/bukkit/event/entity/EntityDamageByBlockEvent.java
@@ -2,6 +2,7 @@
 
 import java.util.Map;
 
+import com.google.common.base.Function;
 import org.bukkit.block.Block;
 import org.bukkit.entity.Entity;
 
@@ -22,8 +23,8 @@
         this.damager = damager;
     }
 
-    public EntityDamageByBlockEvent(final Block damager, final Entity damagee, final DamageCause cause, final Map<DamageModifier, Double> modifiers) {
-        super(damagee, cause, modifiers);
+    public EntityDamageByBlockEvent(final Block damager, final Entity damagee, final DamageCause cause, final Map<DamageModifier, Double> modifiers, final Map<DamageModifier, ? extends Function<? super Double, Double>> modifierFunctions) {
+        super(damagee, cause, modifiers, modifierFunctions);
         this.damager = damager;
     }
 
