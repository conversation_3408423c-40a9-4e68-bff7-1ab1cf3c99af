--- ../src-base/minecraft/org/bukkit/event/entity/EntityDamageByEntityEvent.java
+++ ../src-work/minecraft/org/bukkit/event/entity/EntityDamageByEntityEvent.java
@@ -2,6 +2,7 @@
 
 import java.util.Map;
 
+import com.google.common.base.Function;
 import org.bukkit.entity.Entity;
 
 /**
@@ -21,8 +22,8 @@
         this.damager = damager;
     }
 
-    public EntityDamageByEntityEvent(final Entity damager, final Entity damagee, final DamageCause cause, final Map<DamageModifier, Double> modifiers) {
-        super(damagee, cause, modifiers);
+    public EntityDamageByEntityEvent(final Entity damager, final Entity damagee, final DamageCause cause, final Map<DamageModifier, Double> modifiers, final Map<DamageModifier, ? extends Function<? super Double, Double>> modifierFunctions) {
+        super(damagee, cause, modifiers, modifierFunctions);
         this.damager = damager;
     }
 
