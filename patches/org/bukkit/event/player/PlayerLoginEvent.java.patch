--- ../src-base/minecraft/org/bukkit/event/player/PlayerLoginEvent.java
+++ ../src-work/minecraft/org/bukkit/event/player/PlayerLoginEvent.java
@@ -14,6 +14,7 @@
     private final String hostname;
     private Result result = Result.ALLOWED;
     private String message = "";
+    private final InetAddress realAddress; // Spigot
 
     /**
      * @deprecated Address should be provided in other constructor
@@ -40,19 +41,26 @@
      * @param address The address the player used to connect, provided for
      *     timing issues
      */
-    public PlayerLoginEvent(final Player player, final String hostname, final InetAddress address) {
+    public PlayerLoginEvent(final Player player, final String hostname, final InetAddress address, final InetAddress realAddress) { // Spigot
         super(player);
         this.hostname = hostname;
         this.address = address;
+        // Spigot start
+        this.realAddress = address;
     }
 
+    public PlayerLoginEvent(final Player player, final String hostname, final InetAddress address) {
+        this(player, hostname, address, address);
+        // Spigot end
+    }
+
     /**
      * @deprecated Address and hostname should be provided in other
      *     constructor
      */
     @Deprecated
     public PlayerLoginEvent(final Player player, final Result result, final String message) {
-        this(player, "", null, result, message);
+        this(player, "", null, result, message, null); // Spigot
     }
 
     /**
@@ -65,13 +73,24 @@
      * @param result The result status for this event
      * @param message The message to be displayed if result denies login
      */
-    public PlayerLoginEvent(final Player player, String hostname, final InetAddress address, final Result result, final String message) {
-        this(player, hostname, address);
+    public PlayerLoginEvent(final Player player, String hostname, final InetAddress address, final Result result, final String message, final InetAddress realAddress) { // Spigot
+        this(player, hostname, address, realAddress); // Spigot
         this.result = result;
         this.message = message;
     }
 
+    // Spigot start
     /**
+     * Gets the connection address of this player, regardless of whether it has been spoofed or not.
+     *
+     * @return the player's connection address
+     */
+    public InetAddress getRealAddress() {
+        return realAddress;
+    }
+    // Spigot end
+
+    /**
      * Gets the current result of the login, as an enum
      *
      * @return Current Result of the login
