--- ../src-base/minecraft/org/bukkit/event/player/PlayerChatTabCompleteEvent.java
+++ ../src-work/minecraft/org/bukkit/event/player/PlayerChatTabCompleteEvent.java
@@ -14,6 +14,7 @@
     private final String message;
     private final String lastToken;
     private final Collection<String> completions;
+    private final boolean isPinging;
 
     public PlayerChatTabCompleteEvent(final Player who, final String message, final Collection<String> completions) {
         super(who);
@@ -22,12 +23,44 @@
         this.message = message;
         int i = message.lastIndexOf(' ');
         if (i < 0) {
-            this.lastToken = message;
-        } else {
-            this.lastToken = message.substring(i + 1);
+        	if(message.length() > 0 && message.charAt(0) == '@')
+        	{
+        		this.lastToken = message.substring(1);
+        		this.isPinging = true;
+        	}
+        	else
+        	{
+        		this.lastToken = message;
+        		this.isPinging = false;
+        	}
+        		
+        } 
+        else 
+        {
+        	String lastToken = message.substring(i+1);
+        	if (lastToken.length() > 0 && lastToken.charAt(0) == '@')
+        	{
+        		if (lastToken.length() == 1)
+        		{
+        			lastToken = "";
+        		}
+        		else
+        			lastToken = lastToken.substring(1);
+        		this.isPinging = true;
+        	}
+        	else
+        	{
+        		this.isPinging = false;
+        	}
+        	this.lastToken = lastToken;
         }
         this.completions = completions;
     }
+    
+    public boolean isPinging()
+    {
+    	return this.isPinging;
+    }
 
     /**
      * Gets the chat message being tab-completed.
