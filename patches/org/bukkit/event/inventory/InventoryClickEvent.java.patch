--- ../src-base/minecraft/org/bukkit/event/inventory/InventoryClickEvent.java
+++ ../src-work/minecraft/org/bukkit/event/inventory/InventoryClickEvent.java
@@ -47,6 +47,7 @@
     private static final HandlerList handlers = new HandlerList();
     private final ClickType click;
     private final InventoryAction action;
+    private final Inventory clickedInventory;
     private SlotType slot_type;
     private int whichSlot;
     private int rawSlot;
@@ -58,21 +59,30 @@
         this(view, type, slot, right ? (shift ? ClickType.SHIFT_RIGHT : ClickType.RIGHT) : (shift ? ClickType.SHIFT_LEFT : ClickType.LEFT), InventoryAction.SWAP_WITH_CURSOR);
     }
 
-    public InventoryClickEvent(InventoryView view, SlotType type, int slot, ClickType click, InventoryAction action) {
+    public InventoryClickEvent(InventoryView view, InventoryType.SlotType type, int slot, ClickType click, InventoryAction action) {
         super(view);
         this.slot_type = type;
         this.rawSlot = slot;
+        this.clickedInventory = slot < 0 ? null : (view.getTopInventory() != null && slot < view.getTopInventory().getSize() ? view.getTopInventory() : view.getBottomInventory());
         this.whichSlot = view.convertSlot(slot);
         this.click = click;
         this.action = action;
     }
 
+
     public InventoryClickEvent(InventoryView view, SlotType type, int slot, ClickType click, InventoryAction action, int key) {
         this(view, type, slot, click, action);
         this.hotbarKey = key;
     }
 
     /**
+     * Gets the inventory that was clicked, or null if outside of window
+     * @return The Clicked inventory
+     */
+    public Inventory getClickedInventory() {
+        return clickedInventory;
+    }
+    /**
      * Gets the type of slot that was clicked.
      *
      * @return the slot type
