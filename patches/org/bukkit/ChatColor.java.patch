--- ../src-base/minecraft/org/bukkit/ChatColor.java
+++ ../src-work/minecraft/org/bukkit/ChatColor.java
@@ -3,102 +3,212 @@
 import java.util.Map;
 import java.util.regex.Pattern;
 
-import org.apache.commons.lang.Validate;
+import org.apache.commons.lang3.Validate;
 
 import com.google.common.collect.Maps;
 
 /**
  * All supported color values for chat
  */
-public enum ChatColor {
+public enum ChatColor{
     /**
      * Represents black
      */
-    BLACK('0', 0x00),
+    BLACK('0', 0x00) {
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.BLACK;
+        }
+    },
     /**
      * Represents dark blue
      */
-    DARK_BLUE('1', 0x1),
+    DARK_BLUE('1', 0x1){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_BLUE;
+        }
+    },
     /**
      * Represents dark green
      */
-    DARK_GREEN('2', 0x2),
+    DARK_GREEN('2', 0x2){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_GREEN;
+        }
+    },
     /**
      * Represents dark blue (aqua)
      */
-    DARK_AQUA('3', 0x3),
+    DARK_AQUA('3', 0x3){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_AQUA;
+        }
+    },
     /**
      * Represents dark red
      */
-    DARK_RED('4', 0x4),
+    DARK_RED('4', 0x4){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_RED;
+        }
+    },
     /**
      * Represents dark purple
      */
-    DARK_PURPLE('5', 0x5),
+    DARK_PURPLE('5', 0x5){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_PURPLE;
+        }
+    },
     /**
      * Represents gold
      */
-    GOLD('6', 0x6),
+    GOLD('6', 0x6){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.GOLD;
+        }
+    },
     /**
      * Represents gray
      */
-    GRAY('7', 0x7),
+    GRAY('7', 0x7){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.GRAY;
+        }
+    },
     /**
      * Represents dark gray
      */
-    DARK_GRAY('8', 0x8),
+    DARK_GRAY('8', 0x8){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.DARK_GRAY;
+        }
+    },
     /**
      * Represents blue
      */
-    BLUE('9', 0x9),
+    BLUE('9', 0x9){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.BLUE;
+        }
+    },
     /**
      * Represents green
      */
-    GREEN('a', 0xA),
+    GREEN('a', 0xA){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.GREEN;
+        }
+    },
     /**
      * Represents aqua
      */
-    AQUA('b', 0xB),
+    AQUA('b', 0xB){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.AQUA;
+        }
+    },
     /**
      * Represents red
      */
-    RED('c', 0xC),
+    RED('c', 0xC){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.RED;
+        }
+    },
     /**
      * Represents light purple
      */
-    LIGHT_PURPLE('d', 0xD),
+    LIGHT_PURPLE('d', 0xD){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.LIGHT_PURPLE;
+        }
+    },
     /**
      * Represents yellow
      */
-    YELLOW('e', 0xE),
+    YELLOW('e', 0xE){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.YELLOW;
+        }
+    },
     /**
      * Represents white
      */
-    WHITE('f', 0xF),
+    WHITE('f', 0xF){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.WHITE;
+        }
+    },
     /**
      * Represents magical characters that change around randomly
      */
-    MAGIC('k', 0x10, true),
+    MAGIC('k', 0x10, true){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.MAGIC;
+        }
+    },
     /**
      * Makes the text bold.
      */
-    BOLD('l', 0x11, true),
+    BOLD('l', 0x11, true){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.BOLD;
+        }
+    },
     /**
      * Makes a line appear through the text.
      */
-    STRIKETHROUGH('m', 0x12, true),
+    STRIKETHROUGH('m', 0x12, true){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.STRIKETHROUGH;
+        }
+    },
     /**
      * Makes the text appear underlined.
      */
-    UNDERLINE('n', 0x13, true),
+    UNDERLINE('n', 0x13, true){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.UNDERLINE;
+        }
+    },
     /**
      * Makes the text italic.
      */
-    ITALIC('o', 0x14, true),
+    ITALIC('o', 0x14, true){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.ITALIC;
+        }
+    },
     /**
      * Resets all previous chat colors or formats.
      */
-    RESET('r', 0x15);
+    RESET('r', 0x15){
+        @Override
+        public net.md_5.bungee.api.ChatColor asBungee() {
+            return net.md_5.bungee.api.ChatColor.RESET;
+        }
+    };
 
     /**
      * The special character which prefixes all chat colour codes. Use this if
@@ -125,6 +235,10 @@
         this.toString = new String(new char[] {COLOR_CHAR, code});
     }
 
+    public net.md_5.bungee.api.ChatColor asBungee() {
+        return net.md_5.bungee.api.ChatColor.RESET;
+    };
+
     /**
      * Gets the char value associated with this color
      *
@@ -141,6 +255,8 @@
 
     /**
      * Checks if this code is a format code as opposed to a color code.
+     *
+     * @return whether this ChatColor is a format code
      */
     public boolean isFormat() {
         return isFormat;
@@ -148,6 +264,8 @@
 
     /**
      * Checks if this code is a color code as opposed to a format code.
+     *
+     * @return whether this ChatColor is a color code
      */
     public boolean isColor() {
         return !isFormat && this != RESET;
@@ -157,7 +275,7 @@
      * Gets the color represented by the specified color code
      *
      * @param code Code to check
-     * @return Associative {@link org.bukkit.ChatColor} with the given code,
+     * @return Associative {@link ChatColor} with the given code,
      *     or null if it doesn't exist
      */
     public static ChatColor getByChar(char code) {
@@ -168,7 +286,7 @@
      * Gets the color represented by the specified color code
      *
      * @param code Code to check
-     * @return Associative {@link org.bukkit.ChatColor} with the given code,
+     * @return Associative {@link ChatColor} with the given code,
      *     or null if it doesn't exist
      */
     public static ChatColor getByChar(String code) {
@@ -198,7 +316,7 @@
      * character. The alternate color code character will only be replaced if
      * it is immediately followed by 0-9, A-F, a-f, K-O, k-o, R or r.
      *
-     * @param altColorChar The alternate color code character to replace. Ex: &
+     * @param altColorChar The alternate color code character to replace. Ex: {@literal &}
      * @param textToTranslate Text containing the alternate color code character.
      * @return Text containing the ChatColor.COLOR_CODE color code character.
      */
