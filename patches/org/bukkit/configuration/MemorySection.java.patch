--- ../src-base/minecraft/org/bukkit/configuration/MemorySection.java
+++ ../src-work/minecraft/org/bukkit/configuration/MemorySection.java
@@ -760,7 +760,8 @@
      * @return Full path of the section from its root.
      */
     public static String createPath(ConfigurationSection section, String key) {
-        return createPath(section, key, (section == null) ? null : section.getRoot());
+        Validate.notNull(section, "Cannot create path without a section");
+        return createPath(section, key, section.getRoot());
     }
 
     /**
@@ -784,17 +785,15 @@
         char separator = root.options().pathSeparator();
 
         StringBuilder builder = new StringBuilder();
-        if (section != null) {
-            for (ConfigurationSection parent = section; (parent != null) && (parent != relativeTo); parent = parent.getParent()) {
-                if (builder.length() > 0) {
-                    builder.insert(0, separator);
-                }
-
-                builder.insert(0, parent.getName());
+        for (ConfigurationSection parent = section; parent != null && parent != relativeTo; parent = parent.getParent()) {
+            if (builder.length() > 0) {
+                builder.insert(0, separator);
             }
+
+            builder.insert(0, parent.getName());
         }
 
-        if ((key != null) && (key.length() > 0)) {
+        if (key != null && key.length() > 0) {
             if (builder.length() > 0) {
                 builder.append(separator);
             }
