--- ../src-base/minecraft/org/bukkit/conversations/BooleanPrompt.java
+++ ../src-work/minecraft/org/bukkit/conversations/BooleanPrompt.java
@@ -15,12 +15,13 @@
 
     @Override
     protected boolean isInputValid(ConversationContext context, String input) {
-        String[] accepted = {"true", "false", "on", "off", "yes", "no"};
+        String[] accepted = {"true", "false", "on", "off", "yes", "no" /* Spigot: */, "y", "n", "1", "0", "right", "wrong", "correct", "incorrect", "valid", "invalid"}; // Spigot
         return ArrayUtils.contains(accepted, input.toLowerCase());
     }
 
     @Override
     protected Prompt acceptValidatedInput(ConversationContext context, String input) {
+        if (input.equalsIgnoreCase("y") || input.equals("1") || input.equalsIgnoreCase("right") || input.equalsIgnoreCase("correct") || input.equalsIgnoreCase("valid")) input = "true"; // Spigot
         return acceptValidatedInput(context, BooleanUtils.toBoolean(input));
     }
 
