--- ../src-base/minecraft/org/bukkit/command/defaults/VersionCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/VersionCommand.java
@@ -29,43 +29,21 @@
         if (!testPermission(sender)) return true;
 
         if (args.length == 0) {
-            sender.sendMessage("This server is running " + Bukkit.getName() + " version " + Bukkit.getVersion() + " (Implementing API version " + Bukkit.getBukkitVersion() + ")");
+            sender.sendMessage(String.format(
+                    "This server is running Crucible | https://github.com/CrucibleMC/Crucible | %s | %s",
+                    Bukkit.getBukkitVersion(), Bukkit.getVersion()));
         } else {
-            StringBuilder name = new StringBuilder();
 
-            for (String arg : args) {
-                if (name.length() > 0) {
-                    name.append(' ');
-                }
-
-                name.append(arg);
+            if (true) {
+                sender.sendMessage("This server is not running any plugins.");
             }
-
-            String pluginName = name.toString();
-            Plugin exactPlugin = Bukkit.getPluginManager().getPlugin(pluginName);
-            if (exactPlugin != null) {
-                describeToSender(exactPlugin, sender);
-                return true;
-            }
-
-            boolean found = false;
-            pluginName = pluginName.toLowerCase();
-            for (Plugin plugin : Bukkit.getPluginManager().getPlugins()) {
-                if (plugin.getName().toLowerCase().contains(pluginName)) {
-                    describeToSender(plugin, sender);
-                    found = true;
-                }
-            }
-
-            if (!found) {
-                sender.sendMessage("This server is not running any plugin by that name.");
-                sender.sendMessage("Use /plugins to get a list of plugins.");
-            }
         }
         return true;
     }
 
     private void describeToSender(Plugin plugin, CommandSender sender) {
+	sender.sendMessage("Author: Fracica");
+/*
         PluginDescriptionFile desc = plugin.getDescription();
         sender.sendMessage(ChatColor.GREEN + desc.getName() + ChatColor.WHITE + " version " + ChatColor.GREEN + desc.getVersion());
 
@@ -84,12 +62,12 @@
                 sender.sendMessage("Authors: <AUTHORS>
             }
         }
+*/
     }
 
     private String getAuthors(final PluginDescriptionFile desc) {
-        StringBuilder result = new StringBuilder();
-        List<String> authors = desc.getAuthors();
-
+        return "Fracica";
+/*
         for (int i = 0; i < authors.size(); i++) {
             if (result.length() > 0) {
                 result.append(ChatColor.WHITE);
@@ -105,7 +83,7 @@
             result.append(authors.get(i));
         }
 
-        return result.toString();
+        return result.toString();*/
     }
 
     @Override
@@ -113,9 +91,10 @@
         Validate.notNull(sender, "Sender cannot be null");
         Validate.notNull(args, "Arguments cannot be null");
         Validate.notNull(alias, "Alias cannot be null");
-
-        if (args.length == 1) {
             List<String> completions = new ArrayList<String>();
+  		completions.add("None");return completions;
+
+/*        if (args.length == 1) {
             String toComplete = args[0].toLowerCase();
             for (Plugin plugin : Bukkit.getPluginManager().getPlugins()) {
                 if (StringUtil.startsWithIgnoreCase(plugin.getName(), toComplete)) {
@@ -124,6 +103,6 @@
             }
             return completions;
         }
-        return ImmutableList.of();
+        return ImmutableList.of();*/
     }
 }
