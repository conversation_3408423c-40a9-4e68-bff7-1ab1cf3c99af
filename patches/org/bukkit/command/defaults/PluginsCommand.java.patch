--- ../src-base/minecraft/org/bukkit/command/defaults/PluginsCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/PluginsCommand.java
@@ -40,4 +40,12 @@
 
         return "(" + plugins.length + "): " + pluginList.toString();
     }
+
+    // Spigot Start
+    @Override
+    public java.util.List<String> tabComplete(CommandSender sender, String alias, String[] args) throws IllegalArgumentException
+    {
+        return java.util.Collections.emptyList();
+    }
+    // Spigot End
 }
