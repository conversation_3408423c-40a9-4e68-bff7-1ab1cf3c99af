--- ../src-base/minecraft/org/bukkit/command/defaults/OpCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/OpCommand.java
@@ -4,6 +4,7 @@
 import java.util.Collections;
 import java.util.List;
 
+import io.github.crucible.CrucibleConfigs;
 import org.apache.commons.lang.Validate;
 import org.bukkit.Bukkit;
 import org.bukkit.ChatColor;
@@ -32,10 +33,26 @@
         }
 
         OfflinePlayer player = Bukkit.getOfflinePlayer(args[0]);
-        player.setOp(true);
 
-        Command.broadcastCommandMessage(sender, "Opped " + args[0]);
-        return true;
+        if(CrucibleConfigs.configs.thermos_opConsoleOnly) {
+
+            if(!(sender instanceof Player)) {
+                player.setOp(true);
+
+                Command.broadcastCommandMessage(sender, "Opped " + args[0]);
+                return true;
+            } else {
+                sender.sendMessage(ChatColor.RED + "This command can only be run in the console.");
+                return false;
+            }
+
+        } else {
+            player.setOp(true);
+
+            Command.broadcastCommandMessage(sender, "Opped " + args[0]);
+            return true;
+        }
+
     }
 
     @Override
