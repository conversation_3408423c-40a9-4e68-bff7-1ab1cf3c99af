--- ../src-base/minecraft/org/bukkit/command/defaults/DeopCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/DeopCommand.java
@@ -49,9 +49,9 @@
 
         if (args.length == 1) {
             List<String> completions = new ArrayList<String>();
-            for (OfflinePlayer player : Bukkit.getOfflinePlayers()) {
+            for (OfflinePlayer player : Bukkit.getOperators()) {
                 String playerName = player.getName();
-                if (player.isOp() && StringUtil.startsWithIgnoreCase(playerName, args[0])) {
+                if (StringUtil.startsWithIgnoreCase(playerName, args[0])) {
                     completions.add(playerName);
                 }
             }
