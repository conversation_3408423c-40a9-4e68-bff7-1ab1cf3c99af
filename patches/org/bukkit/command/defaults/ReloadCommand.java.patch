--- ../src-base/minecraft/org/bukkit/command/defaults/ReloadCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/ReloadCommand.java
@@ -2,6 +2,8 @@
 
 import java.util.Arrays;
 
+import net.minecraft.server.MinecraftServer;
+
 import org.bukkit.Bukkit;
 import org.bukkit.ChatColor;
 import org.bukkit.command.Command;
@@ -18,11 +20,26 @@
 
     @Override
     public boolean execute(CommandSender sender, String currentAlias, String[] args) {
+        // Cauldron start - disable reload as it causes many issues with mods
+      if(MinecraftServer.getServer().cauldronConfig.reloadPlugins.getValue()) // Thermos - let's let people reload their plugins...
+      {
         if (!testPermission(sender)) return true;
 
         Bukkit.reload();
         Command.broadcastCommandMessage(sender, ChatColor.GREEN + "Reload complete.");
+      }
+      else
+        sender.sendMessage(ChatColor.RED + "Reload not allowed on a Cauldron server.");
+        // Cauldron end
 
         return true;
     }
+
+    // Spigot Start
+    @Override
+    public java.util.List<String> tabComplete(CommandSender sender, String alias, String[] args) throws IllegalArgumentException
+    {
+        return java.util.Collections.emptyList();
+    }
+    // Spigot End
 }
