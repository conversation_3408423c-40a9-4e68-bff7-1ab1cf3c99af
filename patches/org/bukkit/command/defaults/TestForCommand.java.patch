--- ../src-base/minecraft/org/bukkit/command/defaults/TestForCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/TestForCommand.java
@@ -23,4 +23,16 @@
         sender.sendMessage(ChatColor.RED + "/testfor is only usable by commandblocks with analog output.");
         return true;
     }
+
+    // Spigot Start
+    @Override
+    public java.util.List<String> tabComplete(CommandSender sender, String alias, String[] args) throws IllegalArgumentException
+    {
+        if ( args.length == 0 )
+        {
+            return super.tabComplete( sender, alias, args );
+        }
+        return java.util.Collections.emptyList();
+    }
+    // Spigot End
 }
