--- ../src-base/minecraft/org/bukkit/command/defaults/SpreadPlayersCommand.java
+++ ../src-work/minecraft/org/bukkit/command/defaults/SpreadPlayersCommand.java
@@ -92,7 +92,7 @@
         final int rangeSpread = range(world, distance, xRangeMin, zRangeMin, xRangeMax, zRangeMax, locations);
 
         if (rangeSpread == -1) {
-            sender.sendMessage(String.format("Could not spread %d %s around %s,%s (too many players for space - try using spread of at most %s)", spreadSize, teams ? "teams" : "players", x, z));
+            sender.sendMessage(String.format("Could not spread %d %s around %s,%s (too many players for space - try using spread of at most %s)", spreadSize, teams ? "teams" : "players", x, z, "TODO")); // TODO: Add latest argument
             return false;
         }
 
