--- ../src-base/minecraft/cpw/mods/fml/common/launcher/FMLTweaker.java
+++ ../src-work/minecraft/cpw/mods/fml/common/launcher/FMLTweaker.java
@@ -32,7 +32,9 @@
         System.setProperty("java.net.preferIPv4Stack", "true"); //Lets do this as early as possible. Vanilla does it in Main.main
         try
         {
-            System.setSecurityManager(new FMLSecurityManager());
+        	FMLSecurityManager sm = new FMLSecurityManager();
+        	sm.defman = System.getSecurityManager();
+            System.setSecurityManager(sm);
         }
         catch (SecurityException se)
         {
