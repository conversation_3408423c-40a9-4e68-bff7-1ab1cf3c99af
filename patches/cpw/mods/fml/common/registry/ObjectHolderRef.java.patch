--- ../src-base/minecraft/cpw/mods/fml/common/registry/ObjectHolderRef.java
+++ ../src-work/minecraft/cpw/mods/fml/common/registry/ObjectHolderRef.java
@@ -1,17 +1,18 @@
 package cpw.mods.fml.common.registry;
 
-import java.lang.reflect.Field;
-import java.lang.reflect.Method;
-import java.lang.reflect.Modifier;
-import org.apache.logging.log4j.Level;
 import com.google.common.base.Throwables;
 import cpw.mods.fml.common.FMLLog;
 import cpw.mods.fml.common.registry.GameRegistry.ObjectHolder;
 import net.minecraft.block.Block;
 import net.minecraft.init.Blocks;
 import net.minecraft.item.Item;
+import net.minecraft.util.RegistryNamespaced;
+import org.apache.logging.log4j.Level;
 
+import java.lang.reflect.Field;
+import java.lang.reflect.Method;
 
+
 /**
  * Internal class used in tracking {@link ObjectHolder} references
  *
@@ -74,16 +75,19 @@
     {
         try
         {
-            if (modifiersField == null)
-            {
-                Method getReflectionFactory = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("getReflectionFactory");
-                reflectionFactory = getReflectionFactory.invoke(null);
-                newFieldAccessor = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("newFieldAccessor", Field.class, boolean.class);
-                fieldAccessorSet = Class.forName("sun.reflect.FieldAccessor").getDeclaredMethod("set", Object.class, Object.class);
-                modifiersField = Field.class.getDeclaredField("modifiers");
-                modifiersField.setAccessible(true);
-            }
-            modifiersField.setInt(f, f.getModifiers() & ~Modifier.FINAL);
+            // Crucible start - implement lwjgl3ify patch directly
+//            if (modifiersField == null)
+//            {
+//                Method getReflectionFactory = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("getReflectionFactory");
+//                reflectionFactory = getReflectionFactory.invoke(null);
+//                newFieldAccessor = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("newFieldAccessor", Field.class, boolean.class);
+//                fieldAccessorSet = Class.forName("sun.reflect.FieldAccessor").getDeclaredMethod("set", Object.class, Object.class);
+//                modifiersField = Field.class.getDeclaredField("modifiers");
+//                modifiersField.setAccessible(true);
+//            }
+//            modifiersField.setInt(f, f.getModifiers() & ~Modifier.FINAL);
+            f.setAccessible(true);
+            // Crucible end
         } catch (Exception e)
         {
             throw Throwables.propagate(e);
@@ -94,39 +98,72 @@
     {
         return isBlock || isItem;
     }
+
     public void apply()
     {
+        // Crucible start - implement lwjgl3ify patch directly
+//        Object thing;
+//        if (isBlock)
+//        {
+//            thing = GameData.getBlockRegistry().getObject(injectedObject);
+//            if (thing == Blocks.air)
+//            {
+//                thing = null;
+//            }
+//        }
+//        else if (isItem)
+//        {
+//            thing = GameData.getItemRegistry().getObject(injectedObject);
+//        }
+//        else
+//        {
+//            thing = null;
+//        }
+//
+//        if (thing == null)
+//        {
+//            FMLLog.getLogger().log(Level.DEBUG, "Unable to lookup {} for {}. This means the object wasn't registered. It's likely just mod options.", injectedObject, field);
+//            return;
+//        }
+//        try
+//        {
+//            Object fieldAccessor = newFieldAccessor.invoke(reflectionFactory, field, false);
+//            fieldAccessorSet.invoke(fieldAccessor, null, thing);
+//        }
+//        catch (Exception e)
+//        {
+//            FMLLog.log(Level.WARN, e, "Unable to set %s with value %s (%s)", this.field, thing, this.injectedObject);
+//        }
         Object thing;
-        if (isBlock)
-        {
-            thing = GameData.getBlockRegistry().getObject(injectedObject);
-            if (thing == Blocks.air)
-            {
+        RegistryNamespaced registry;
+        if (isBlock) {
+            registry = GameData.getBlockRegistry();
+            thing = registry.getObject(injectedObject);
+            if (thing == Blocks.air) {
                 thing = null;
             }
-        }
-        else if (isItem)
-        {
-            thing = GameData.getItemRegistry().getObject(injectedObject);
-        }
-        else
-        {
+        } else if (isItem) {
+            registry = GameData.getItemRegistry();
+            thing = registry.getObject(injectedObject);
+        } else {
             thing = null;
         }
 
-        if (thing == null)
-        {
-            FMLLog.getLogger().log(Level.DEBUG, "Unable to lookup {} for {}. This means the object wasn't registered. It's likely just mod options.", injectedObject, field);
+        if (thing == null) {
+            FMLLog.getLogger()
+                    .log(
+                            Level.DEBUG,
+                            "Unable to lookup {} for {}. This means the object wasn't registered. It's likely just mod options.",
+                            injectedObject,
+                            field);
             return;
         }
-        try
-        {
-            Object fieldAccessor = newFieldAccessor.invoke(reflectionFactory, field, false);
-            fieldAccessorSet.invoke(fieldAccessor, null, thing);
-        }
-        catch (Exception e)
-        {
+        try {
+            field.set(null, thing);
+            FMLLog.finer("Set field " + field.toString() + " to " + thing);
+        } catch (Throwable e) {
             FMLLog.log(Level.WARN, e, "Unable to set %s with value %s (%s)", this.field, thing, this.injectedObject);
         }
+        // Crucible end
     }
 }
