--- ../src-base/minecraft/cpw/mods/fml/common/registry/GameRegistry.java
+++ ../src-work/minecraft/cpw/mods/fml/common/registry/GameRegistry.java
@@ -24,6 +24,7 @@
 import java.util.Map;
 import java.util.Random;
 import java.util.Set;
+import java.util.HashMap; // Cauldron
 
 import net.minecraft.block.Block;
 import net.minecraft.item.Item;
@@ -67,6 +68,11 @@
     private static List<IFuelHandler> fuelHandlers = Lists.newArrayList();
     private static List<IWorldGenerator> sortedGeneratorList;
 
+    // Cauldron start
+    private static Map<String, Boolean> configWorldGenCache = new HashMap<String, Boolean>();
+    private static Map<String, String> worldGenMap = new HashMap<String, String>();
+    // Cauldron end
+
     /**
      * Register a world generator - something that inserts new block types into the world
      *
@@ -76,12 +82,18 @@
      */
     public static void registerWorldGenerator(IWorldGenerator generator, int modGenerationWeight)
     {
+        // Cauldron start - mod id's are not available during generateWorld so we must capture them here
+        String modId = Loader.instance().activeModContainer().getModId();
+        modId = modId.replaceAll("[^A-Za-z0-9]", ""); // remove all non-digits/alphanumeric
+        modId = modId.replace(" ", "_");
         worldGenerators.add(generator);
         worldGeneratorIndex.put(generator, modGenerationWeight);
         if (sortedGeneratorList != null)
         {
             sortedGeneratorList = null;
         }
+        worldGenMap.put(generator.getClass().getName(), modId);
+        // Cauldron end
     }
 
     /**
@@ -106,11 +118,27 @@
         long zSeed = fmlRandom.nextLong() >> 2 + 1L;
         long chunkSeed = (xSeed * chunkX + zSeed * chunkZ) ^ worldSeed;
 
-        for (IWorldGenerator generator : sortedGeneratorList)
+        boolean before = ((net.minecraft.world.WorldServer) world).theChunkProviderServer.loadChunkOnProvideRequest; // Cauldron store value
+        ((net.minecraft.world.WorldServer) world).theChunkProviderServer.loadChunkOnProvideRequest = true; // Cauldron load chunks on provide requests
+        for (IWorldGenerator generator : worldGenerators)
         {
-            fmlRandom.setSeed(chunkSeed);
-            generator.generate(fmlRandom, chunkX, chunkZ, world, chunkGenerator, chunkProvider);
+            // Cauldron start
+            if (!configWorldGenCache.containsKey(generator.getClass().getName()))
+            {
+                String modId = worldGenMap.get(generator.getClass().getName());
+                String generatorName = "";
+                generatorName = modId + "-" + generator.getClass().getSimpleName();
+                boolean generatorEnabled = world.cauldronConfig.getBoolean("worldgen-" + generatorName, true);
+                configWorldGenCache.put(generator.getClass().getName(), generatorEnabled);
+            }
+            if (configWorldGenCache.get(generator.getClass().getName()))
+            {
+                fmlRandom.setSeed(chunkSeed);
+                generator.generate(fmlRandom, chunkX, chunkZ, world, chunkGenerator, chunkProvider);
+            }
         }
+        ((net.minecraft.world.WorldServer)world).theChunkProviderServer.loadChunkOnProvideRequest = before; // reset
+        // Cauldron end
     }
 
     private static void computeSortedGeneratorList()
