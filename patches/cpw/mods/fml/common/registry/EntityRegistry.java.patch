--- ../src-base/minecraft/cpw/mods/fml/common/registry/EntityRegistry.java
+++ ../src-work/minecraft/cpw/mods/fml/common/registry/EntityRegistry.java
@@ -41,6 +41,10 @@
 import cpw.mods.fml.common.ModContainer;
 import cpw.mods.fml.common.network.internal.FMLMessage.EntitySpawnMessage;
 
+// Cauldron start
+
+// Cauldron end
+
 public class EntityRegistry
 {
     public class EntityRegistration
@@ -118,6 +122,8 @@
     private ListMultimap<ModContainer, EntityRegistration> entityRegistrations = ArrayListMultimap.create();
     private Map<String,ModContainer> entityNames = Maps.newHashMap();
     private BiMap<Class<? extends Entity>, EntityRegistration> entityClassRegistrations = HashBiMap.create();
+    public static Map<Class <? extends Entity>, String> entityTypeMap = Maps.newHashMap(); // Cauldron - used by CraftCustomEntity
+    public static Map<String, Class <? extends Entity>> entityClassMap = Maps.newHashMap(); // Cauldron - used by CraftWorld
     public static EntityRegistry instance()
     {
         return INSTANCE;
@@ -147,6 +153,7 @@
     public static void registerModEntity(Class<? extends Entity> entityClass, String entityName, int id, Object mod, int trackingRange, int updateFrequency, boolean sendsVelocityUpdates)
     {
         instance().doModEntityRegistration(entityClass, entityName, id, mod, trackingRange, updateFrequency, sendsVelocityUpdates);
+        registerBukkitType(entityClass, entityName); // Cauldron - register EntityType for Bukkit
     }
 
     @SuppressWarnings("unchecked")
@@ -197,6 +204,7 @@
         }
         id = instance().validateAndClaimId(id);
         EntityList.addMapping(entityClass, entityName, id);
+        registerBukkitType(entityClass, entityName); // Cauldron - register EntityType for Bukkit
     }
 
     private int validateAndClaimId(int id)
@@ -249,8 +257,38 @@
         }
         instance().validateAndClaimId(id);
         EntityList.addMapping(entityClass, entityName, id, backgroundEggColour, foregroundEggColour);
+        registerBukkitType(entityClass, entityName); // Cauldron - register EntityType for Bukkit
     }
 
+    // Cauldron start
+    private static void registerBukkitType(Class <? extends Entity > entityClass, String entityName)
+    {
+        ModContainer activeModContainer = Loader.instance().activeModContainer();
+        String modId = "unknown";
+        // fixup bad entity names from mods
+        if (entityName.contains("."))
+        {
+            if ((entityName.indexOf(".") + 1) < entityName.length())
+                entityName = entityName.substring(entityName.indexOf(".") + 1, entityName.length());
+        }
+        entityName = entityName.replace("entity", "");
+        if (entityName.startsWith("ent"))
+            entityName = entityName.replace("ent", "");
+        entityName = entityName.replaceAll("[^A-Za-z0-9]", ""); // remove all non-digits/alphanumeric
+        if (activeModContainer != null)
+            modId = activeModContainer.getModId();
+        entityName = modId + "-" + entityName;
+        entityTypeMap.put(entityClass, entityName);
+        entityClassMap.put(entityName, entityClass);
+    }
+
+    // used by CraftCustomEntity
+    public static String getCustomEntityTypeName(Class <? extends Entity > entityClass)
+    {
+        return entityTypeMap.get(entityClass);
+    }
+    // Cauldron end
+
     public static void addSpawn(Class <? extends EntityLiving > entityClass, int weightedProb, int min, int max, EnumCreatureType typeOfCreature, BiomeGenBase... biomes)
     {
         for (BiomeGenBase biome : biomes)
