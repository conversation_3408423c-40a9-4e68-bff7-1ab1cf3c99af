--- ../src-base/minecraft/cpw/mods/fml/common/registry/ItemStackHolderRef.java
+++ ../src-work/minecraft/cpw/mods/fml/common/registry/ItemStackHolderRef.java
@@ -1,19 +1,15 @@
 package cpw.mods.fml.common.registry;
 
-import java.lang.reflect.Field;
-import java.lang.reflect.Method;
-import java.lang.reflect.Modifier;
-
-import net.minecraft.item.ItemStack;
-
-import org.apache.logging.log4j.Level;
-
 import com.google.common.base.Throwables;
-
 import cpw.mods.fml.common.FMLLog;
 import cpw.mods.fml.common.registry.GameRegistry.ItemStackHolder;
+import net.minecraft.item.ItemStack;
+import org.apache.logging.log4j.Level;
 
+import java.lang.reflect.Field;
+import java.lang.reflect.Method;
 
+
 /**
  * Internal class used in tracking {@link ItemStackHolder} references
  *
@@ -44,16 +40,19 @@
     {
         try
         {
-            if (modifiersField == null)
-            {
-                Method getReflectionFactory = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("getReflectionFactory");
-                reflectionFactory = getReflectionFactory.invoke(null);
-                newFieldAccessor = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("newFieldAccessor", Field.class, boolean.class);
-                fieldAccessorSet = Class.forName("sun.reflect.FieldAccessor").getDeclaredMethod("set", Object.class, Object.class);
-                modifiersField = Field.class.getDeclaredField("modifiers");
-                modifiersField.setAccessible(true);
-            }
-            modifiersField.setInt(f, f.getModifiers() & ~Modifier.FINAL);
+            // Crucible start - implement lwjgl3ify patch directly
+//            if (modifiersField == null)
+//            {
+//                Method getReflectionFactory = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("getReflectionFactory");
+//                reflectionFactory = getReflectionFactory.invoke(null);
+//                newFieldAccessor = Class.forName("sun.reflect.ReflectionFactory").getDeclaredMethod("newFieldAccessor", Field.class, boolean.class);
+//                fieldAccessorSet = Class.forName("sun.reflect.FieldAccessor").getDeclaredMethod("set", Object.class, Object.class);
+//                modifiersField = Field.class.getDeclaredField("modifiers");
+//                modifiersField.setAccessible(true);
+//            }
+//            modifiersField.setInt(f, f.getModifiers() & ~Modifier.FINAL);
+            f.setAccessible(true);
+            // Crucible end
         } catch (Exception e)
         {
             throw Throwables.propagate(e);
@@ -62,23 +61,53 @@
 
     public void apply()
     {
+        // Crucible start - implement lwjgl3ify patch directly
+//        ItemStack is;
+//        try
+//        {
+//            is = GameRegistry.makeItemStack(itemName, meta, 1, serializednbt);
+//        } catch (RuntimeException e)
+//        {
+//            FMLLog.getLogger().log(Level.ERROR, "Caught exception processing itemstack {},{},{} in annotation at {}.{}", itemName, meta, serializednbt,field.getClass().getName(),field.getName());
+//            throw e;
+//        }
+//        try
+//        {
+//            Object fieldAccessor = newFieldAccessor.invoke(reflectionFactory, field, false);
+//            fieldAccessorSet.invoke(fieldAccessor, null, is);
+//        }
+//        catch (Exception e)
+//        {
+//            FMLLog.getLogger().log(Level.WARN, "Unable to set {} with value {},{},{}", this.field, this.itemName, this.meta, this.serializednbt);
+//        }
         ItemStack is;
-        try
-        {
+        try {
             is = GameRegistry.makeItemStack(itemName, meta, 1, serializednbt);
-        } catch (RuntimeException e)
-        {
-            FMLLog.getLogger().log(Level.ERROR, "Caught exception processing itemstack {},{},{} in annotation at {}.{}", itemName, meta, serializednbt,field.getClass().getName(),field.getName());
+        } catch (RuntimeException e) {
+            FMLLog.getLogger()
+              .log(
+                Level.ERROR,
+                "Caught exception processing itemstack {},{},{} in annotation at {}.{}",
+                itemName,
+                meta,
+                serializednbt,
+                field.getClass()
+                  .getName(),
+                field.getName());
             throw e;
         }
-        try
-        {
-            Object fieldAccessor = newFieldAccessor.invoke(reflectionFactory, field, false);
-            fieldAccessorSet.invoke(fieldAccessor, null, is);
+        try {
+            field.set(null, is);
+        } catch (Throwable e) {
+            FMLLog.getLogger()
+              .log(
+                Level.WARN,
+                "Unable to set {} with value {},{},{}",
+                this.field,
+                this.itemName,
+                this.meta,
+                this.serializednbt);
         }
-        catch (Exception e)
-        {
-            FMLLog.getLogger().log(Level.WARN, "Unable to set {} with value {},{},{}", this.field, this.itemName, this.meta, this.serializednbt);
-        }
+        // Crucible end
     }
 }
