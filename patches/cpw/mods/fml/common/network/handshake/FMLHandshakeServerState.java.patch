--- ../src-base/minecraft/cpw/mods/fml/common/network/handshake/FMLHandshakeServerState.java
+++ ../src-work/minecraft/cpw/mods/fml/common/network/handshake/FMLHandshakeServerState.java
@@ -1,5 +1,6 @@
 package cpw.mods.fml.common.network.handshake;
 
+import io.github.crucible.CrucibleConfigs;
 import io.netty.channel.ChannelFutureListener;
 import io.netty.channel.ChannelHandlerContext;
 import cpw.mods.fml.common.FMLLog;
@@ -19,6 +20,10 @@
         {
             NetworkDispatcher dispatcher = ctx.channel().attr(NetworkDispatcher.FML_DISPATCHER).get();
             int overrideDim = dispatcher.serverInitiateHandshake();
+            if(overrideDim == -100000)
+            {
+            	return ERROR;
+            }
             ctx.writeAndFlush(FMLHandshakeMessage.makeCustomChannelRegistration(NetworkRegistry.INSTANCE.channelNamesFor(Side.SERVER))).addListener(ChannelFutureListener.FIRE_EXCEPTION_ON_FAILURE);
             ctx.writeAndFlush(new FMLHandshakeMessage.ServerHello(overrideDim)).addListener(ChannelFutureListener.FIRE_EXCEPTION_ON_FAILURE);
             return HELLO;
@@ -37,7 +42,11 @@
             }
 
             FMLHandshakeMessage.ModList client = (FMLHandshakeMessage.ModList)msg;
-            FMLLog.info("Client attempting to join with %d mods : %s", client.modListSize(), client.modListAsString());
+            if (CrucibleConfigs.configs.thermos_logging_clientModList) {
+                FMLLog.info("Client attempting to join with %d mods : %s", client.modListSize(), client.modListAsString());
+            } else {
+                FMLLog.info("Client attempting to join with %d mods", client.modListSize());
+            }
             String result = FMLNetworkHandler.checkModList(client, Side.CLIENT);
             if (result != null)
             {
