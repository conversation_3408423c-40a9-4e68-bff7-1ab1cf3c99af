--- ../src-base/minecraft/cpw/mods/fml/common/network/internal/HandshakeCompletionHandler.java
+++ ../src-work/minecraft/cpw/mods/fml/common/network/internal/HandshakeCompletionHandler.java
@@ -13,8 +13,14 @@
     @Override
     protected void channelRead0(ChannelHandlerContext ctx, CompleteHandshake msg) throws Exception
     {
-        NetworkDispatcher dispatcher = ctx.channel().attr(NetworkDispatcher.FML_DISPATCHER).getAndRemove();
-        dispatcher.completeHandshake(msg.target);
+        // Cauldron start - attempt to fix race condition with attr being null
+        io.netty.util.Attribute<NetworkDispatcher> attr = ctx.channel().attr(NetworkDispatcher.FML_DISPATCHER);
+        if (attr != null)
+        {
+            NetworkDispatcher dispatcher = attr.getAndRemove();
+            if (dispatcher != null) dispatcher.completeHandshake(msg.target);
+        }
+        // <PERSON><PERSON>dron end
     }
 
     @Override
