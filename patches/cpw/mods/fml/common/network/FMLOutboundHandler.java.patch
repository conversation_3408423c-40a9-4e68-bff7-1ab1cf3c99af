--- ../src-base/minecraft/cpw/mods/fml/common/network/FMLOutboundHandler.java
+++ ../src-work/minecraft/cpw/mods/fml/common/network/FMLOutboundHandler.java
@@ -6,6 +6,7 @@
 import io.netty.channel.embedded.EmbeddedChannel;
 import io.netty.util.AttributeKey;
 import java.util.List;
+import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.entity.player.EntityPlayerMP;
 import net.minecraft.network.NetworkManager;
 import com.google.common.collect.ImmutableList;
@@ -97,9 +98,9 @@
             @Override
             public void validateArgs(Object args)
             {
-                if (!(args instanceof EntityPlayerMP))
+                if (!(args instanceof EntityPlayer))
                 {
-                    throw new RuntimeException("PLAYER target expects a Player arg");
+                    throw new RuntimeException("PLAYER target expects a Player arg... it is a " + args.getClass().getName());
                 }
             }
             @Override
