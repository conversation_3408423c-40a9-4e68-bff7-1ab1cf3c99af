--- ../src-base/minecraft/cpw/mods/fml/relauncher/CoreModManager.java
+++ ../src-work/minecraft/cpw/mods/fml/relauncher/CoreModManager.java
@@ -7,7 +7,8 @@
  * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
  *
  * Contributors:
- *     cpw - implementation
+ *        cpw - implementation
+ * eigenraven - java 9 compatibility
  */
 
 package cpw.mods.fml.relauncher;
@@ -16,7 +17,7 @@
 import java.io.FileFilter;
 import java.io.FilenameFilter;
 import java.io.IOException;
-import java.lang.reflect.InvocationTargetException;
+import java.lang.reflect.Field;
 import java.lang.reflect.Method;
 import java.net.MalformedURLException;
 import java.net.URL;
@@ -31,6 +32,9 @@
 import java.util.jar.Attributes;
 import java.util.jar.JarFile;
 
+import io.github.crucible.CrucibleModContainer;
+import io.github.crucible.bootstrap.CrucibleCoremodHook;
+import io.github.crucible.bootstrap.CrucibleServerMainHook;
 import net.minecraft.launchwrapper.ITweaker;
 import net.minecraft.launchwrapper.Launch;
 import net.minecraft.launchwrapper.LaunchClassLoader;
@@ -57,10 +61,11 @@
 import cpw.mods.fml.relauncher.IFMLLoadingPlugin.TransformerExclusions;
 
 public class CoreModManager {
+
     private static final Attributes.Name COREMODCONTAINSFMLMOD = new Attributes.Name("FMLCorePluginContainsFMLMod");
     private static final Attributes.Name MODTYPE = new Attributes.Name("ModType");
     private static final Attributes.Name MODSIDE = new Attributes.Name("ModSide");
-    private static String[] rootPlugins = { "cpw.mods.fml.relauncher.FMLCorePlugin", "net.minecraftforge.classloading.FMLForgePlugin" };
+    private static String[] rootPlugins = { "cpw.mods.fml.relauncher.FMLCorePlugin", "net.minecraftforge.classloading.FMLForgePlugin", "pw.prok.imagine.ImagineLoadingPlugin" };
     private static List<String> loadedCoremods = Lists.newArrayList();
     private static List<FMLPluginWrapper> loadPlugins;
     private static boolean deobfuscatedEnvironment;
@@ -70,6 +75,7 @@
     private static List<String> accessTransformers = Lists.newArrayList();
 
     private static class FMLPluginWrapper implements ITweaker {
+
         public final String name;
         public final IFMLLoadingPlugin coreModInstance;
         public final List<String> predepends;
@@ -99,14 +105,17 @@
         }
 
         @Override
-        public void injectIntoClassLoader(LaunchClassLoader classLoader)
-        {
-            FMLRelaunchLog.fine("Injecting coremod %s {%s} class transformers", name, coreModInstance.getClass().getName());
-            if (coreModInstance.getASMTransformerClass() != null) for (String transformer : coreModInstance.getASMTransformerClass())
-            {
-                FMLRelaunchLog.finer("Registering transformer %s", transformer);
-                classLoader.registerTransformer(transformer);
-            }
+        public void injectIntoClassLoader(LaunchClassLoader classLoader) {
+            FMLRelaunchLog.info(
+              "Injecting coremod %s {%s} class transformers",
+              name,
+              coreModInstance.getClass()
+                .getName());
+            if (coreModInstance.getASMTransformerClass() != null)
+                for (String transformer : coreModInstance.getASMTransformerClass()) {
+                    FMLRelaunchLog.finer("Registering transformer %s", transformer);
+                    classLoader.registerTransformer(transformer);
+                }
             FMLRelaunchLog.fine("Injection complete");
 
             FMLRelaunchLog.fine("Running coremod plugin for %s {%s}", name, coreModInstance.getClass().getName());
@@ -160,6 +169,9 @@
 
     }
 
+    // Cauldron - group output of @MCVersion warnings
+    private static List<String> noVersionAnnotationCoreMods = new ArrayList<String>();
+
     public static void handleLaunch(File mcDir, LaunchClassLoader classLoader, FMLTweaker tweaker)
     {
         CoreModManager.mcDir = mcDir;
@@ -206,6 +218,9 @@
         }
 
         FMLRelaunchLog.fine("All fundamental core mods are successfully located");
+        // Crucible start - too lazy for creating a coremod
+        CrucibleCoremodHook.coremodHandleLaunch(mcDir, classLoader, tweaker);
+        // Crucible end
         // Now that we have the root plugins loaded - lets see what else might
         // be around
         String commandLineCoremods = System.getProperty("fml.coreMods.load", "");
@@ -219,7 +234,19 @@
             loadCoreMod(classLoader, coreModClassName, null);
         }
         discoverCoreMods(mcDir, classLoader);
-
+        // Cauldron start - group output of @MCVersion warnings
+        if (!noVersionAnnotationCoreMods.isEmpty())
+        {
+            FMLRelaunchLog
+              .warning("The following coremods do not have a @MCVersion annotation. They may cause problems if this is not the correct version of Minecraft for them.");
+            StringBuilder sb = new StringBuilder("Missing @MCVersion: ");
+            for (String className : noVersionAnnotationCoreMods)
+            {
+                sb.append(className).append("  ");
+            }
+            FMLRelaunchLog.warning(sb.toString());
+        }
+        // Cauldron end
     }
 
     private static void discoverCoreMods(File mcDir, LaunchClassLoader classLoader)
@@ -336,6 +363,10 @@
             String cascadedTweaker = mfAttributes.getValue("TweakClass");
             if (cascadedTweaker != null)
             {
+                if ("fastcraft.Tweaker".equals(cascadedTweaker) && !"false".equals(System.getProperty("thermos.fastcraft.disable", "true"))) {
+                    FMLRelaunchLog.info("Found FastCraft tweaker, skipping...");
+                    continue;
+                }
                 FMLRelaunchLog.info("Loading tweaker %s from %s", cascadedTweaker, coreMod.getName());
                 Integer sortOrder = Ints.tryParse(Strings.nullToEmpty(mfAttributes.getValue("TweakOrder")));
                 sortOrder = (sortOrder == null ? Integer.valueOf(0) : sortOrder);
@@ -365,6 +396,10 @@
                 FMLRelaunchLog.fine("Not found coremod data in %s", coreMod.getName());
                 continue;
             }
+            if ("fastcraft.LoadingPlugin".equals(fmlCorePlugin) && !"false".equals(System.getProperty("thermos.fastcraft.disable", "true"))) {
+                FMLRelaunchLog.warning("Found FastCraft coremod, skipping...");
+                continue;
+            }
             // Support things that are mod jars, but not FML mod jars
             try
             {
@@ -377,40 +412,76 @@
                 else
                 {
                     FMLRelaunchLog.finer("Found FMLCorePluginContainsFMLMod marker in %s, it will be examined later for regular @Mod instances",
-                            coreMod.getName());
+                      coreMod.getName());
                     reparsedCoremods.add(coreMod.getName());
                 }
             }
             catch (MalformedURLException e)
             {
                 FMLRelaunchLog.log(Level.ERROR, e, "Unable to convert file into a URL. weird");
+
                 continue;
             }
             loadCoreMod(classLoader, fmlCorePlugin, coreMod);
         }
     }
 
+    // Lwjgl3ify start - support code
     private static Method ADDURL;
+    private static void addUrlToClassloader(ClassLoader loader, URL coreModUrl) {
+        try {
+            if (loader instanceof URLClassLoader) {
+                if (ADDURL == null) {
+                    ADDURL = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
+                    ADDURL.setAccessible(true);
+                }
+                ADDURL.invoke(loader, coreModUrl);
+            } else {
+                Field ucpField;
+                try {
+                    // Java 8-11
+                    ucpField = loader.getClass()
+                      .getDeclaredField("ucp");
+                } catch (NoSuchFieldException e) {
+                    // Java 17
+                    ucpField = loader.getClass()
+                      .getSuperclass()
+                      .getDeclaredField("ucp");
+                }
+                ucpField.setAccessible(true);
+                final Object ucp = ucpField.get(loader);
+                final Method urlAdder = ucp.getClass()
+                  .getDeclaredMethod("addURL", URL.class);
+                urlAdder.invoke(ucp, coreModUrl);
+            }
+        } catch (ReflectiveOperationException e) {
+            throw new RuntimeException("Couldn't add url to classpath in loader " + loader.getClass(), e);
+        }
+    }
+    // Lwjgl3ify end
 
-    private static void handleCascadingTweak(File coreMod, JarFile jar, String cascadedTweaker, LaunchClassLoader classLoader, Integer sortingOrder)
-    {
-        try
-        {
+    private static void handleCascadingTweak(File coreMod, JarFile jar, String cascadedTweaker,
+                                             LaunchClassLoader classLoader, Integer sortingOrder) {
+        try {
             // Have to manually stuff the tweaker into the parent classloader
-            if (ADDURL == null)
-            {
-                ADDURL = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
-                ADDURL.setAccessible(true);
-            }
-            ADDURL.invoke(classLoader.getClass().getClassLoader(), coreMod.toURI().toURL());
-            classLoader.addURL(coreMod.toURI().toURL());
+            // Lwjgl3ify start
+            URL coreModUrl = coreMod.toURI()
+              .toURL();
+            ClassLoader myLoader = classLoader.getClass()
+              .getClassLoader();
+            addUrlToClassloader(myLoader, coreModUrl);
+            classLoader.addURL(coreModUrl);
+            // Lwjgl3ify end
             CoreModManager.tweaker.injectCascadingTweak(cascadedTweaker);
-            tweakSorting.put(cascadedTweaker,sortingOrder);
+            tweakSorting.put(cascadedTweaker, sortingOrder);
+        } catch (Exception e) {
+            FMLRelaunchLog.log(
+              Level.INFO,
+              e,
+              "There was a problem trying to load the mod dir tweaker %s",
+              coreMod.getAbsolutePath());
+            e.printStackTrace();
         }
-        catch (Exception e)
-        {
-            FMLRelaunchLog.log(Level.INFO, e, "There was a problem trying to load the mod dir tweaker %s", coreMod.getAbsolutePath());
-        }
     }
 
     /**
@@ -467,19 +538,22 @@
             MCVersion requiredMCVersion = coreModClazz.getAnnotation(IFMLLoadingPlugin.MCVersion.class);
             if (!Arrays.asList(rootPlugins).contains(coreModClass) && (requiredMCVersion == null || Strings.isNullOrEmpty(requiredMCVersion.value())))
             {
-                FMLRelaunchLog.log(Level.WARN, "The coremod %s does not have a MCVersion annotation, it may cause issues with this version of Minecraft",
-                        coreModClass);
+                // Cauldron start - group output of @MCVersion warnings
+                // FMLRelaunchLog.log(Level.WARN, "The coremod %s does not have a MCVersion annotation, it may cause issues with this version of Minecraft",
+                //        coreModClass);
+                noVersionAnnotationCoreMods.add(coreModClass);
+                // Cauldron end
             }
             else if (requiredMCVersion != null && !FMLInjectionData.mccversion.equals(requiredMCVersion.value()))
             {
                 FMLRelaunchLog.log(Level.ERROR, "The coremod %s is requesting minecraft version %s and minecraft is %s. It will be ignored.", coreModClass,
-                        requiredMCVersion.value(), FMLInjectionData.mccversion);
+                  requiredMCVersion.value(), FMLInjectionData.mccversion);
                 return null;
             }
             else if (requiredMCVersion != null)
             {
                 FMLRelaunchLog.log(Level.DEBUG, "The coremod %s requested minecraft version %s and minecraft is %s. It will be loaded.", coreModClass,
-                        requiredMCVersion.value(), FMLInjectionData.mccversion);
+                  requiredMCVersion.value(), FMLInjectionData.mccversion);
             }
             TransformerExclusions trExclusions = coreModClazz.getAnnotation(IFMLLoadingPlugin.TransformerExclusions.class);
             if (trExclusions != null)
