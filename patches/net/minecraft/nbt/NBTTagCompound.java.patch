--- ../src-base/minecraft/net/minecraft/nbt/NBTTagCompound.java
+++ ../src-work/minecraft/net/minecraft/nbt/NBTTagCompound.java
@@ -3,11 +3,10 @@
 import java.io.DataInput;
 import java.io.DataOutput;
 import java.io.IOException;
-import java.util.HashMap;
-import java.util.Iterator;
-import java.util.Map;
-import java.util.Set;
+import java.util.*;
 import java.util.concurrent.Callable;
+import java.util.regex.Pattern;
+
 import net.minecraft.crash.CrashReport;
 import net.minecraft.crash.CrashReportCategory;
 import net.minecraft.util.ReportedException;
@@ -17,9 +16,22 @@
 public class NBTTagCompound extends NBTBase
 {
     private static final Logger logger = LogManager.getLogger();
-    private Map tagMap = new HashMap();
+    private Map<String, NBTBase> tagMap = null;
     private static final String __OBFID = "CL_00001215";
 
+    public NBTTagCompound()
+    {
+    	this (false);
+    }
+    
+    public NBTTagCompound(boolean _copy)
+    {
+    	if (!_copy)
+    	{
+    		tagMap = new HashMap<String, NBTBase>();
+    	}
+    }
+    
     void write(DataOutput p_74734_1_) throws IOException
     {
         Iterator iterator = this.tagMap.keySet().iterator();
@@ -336,15 +348,12 @@
 
     public NBTBase copy()
     {
-        NBTTagCompound nbttagcompound = new NBTTagCompound();
-        Iterator iterator = this.tagMap.keySet().iterator();
+        NBTTagCompound nbttagcompound = new NBTTagCompound(true);
+        nbttagcompound.tagMap = new HashMap<String, NBTBase>((int)(this.tagMap.size()*1.35)+1); //compaction factor
+        
+        for(Map.Entry<String, NBTBase> s : this.tagMap.entrySet())
+            nbttagcompound.setTag(s.getKey(), s.getValue().copy());
 
-        while (iterator.hasNext())
-        {
-            String s = (String)iterator.next();
-            nbttagcompound.setTag(s, ((NBTBase)this.tagMap.get(s)).copy());
-        }
-
         return nbttagcompound;
     }
 
@@ -407,4 +416,72 @@
             throw new ReportedException(crashreport);
         }
     }
+
+    /**
+     * Merges copies of data contained in {@code other} into this compound tag.
+     */
+    public void merge(NBTTagCompound other)
+    {
+        for (String key : other.tagMap.keySet())
+        {
+            NBTBase otherValue = other.tagMap.get(key);
+            if (otherValue.getId() == 10)
+            {
+                if (this.hasKey(key, 10))
+                {
+                    NBTTagCompound value = this.getCompoundTag(key);
+                    value.merge((NBTTagCompound)otherValue);
+                }
+                else
+                {
+                    this.setTag(key, otherValue.copy());
+                }
+            }
+            else
+            {
+                this.setTag(key, otherValue.copy());
+            }
+        }
+    }
+
+    @Override
+    public String crucible_toString()
+    {
+        StringBuilder sb = new StringBuilder("{");
+        Collection<String> keys = this.tagMap.keySet();
+        for (String key : keys)
+        {
+            if (sb.length() != 1)
+            {
+                sb.append(',');
+            }
+
+            sb.append(handleString(key)).append(':').append((this.tagMap.get(key)).crucible_toString());
+        }
+        return sb.append('}').toString();
+    }
+
+    private static final Pattern ALLOWED_CHARS = Pattern.compile("[A-Za-z0-9._+-]+");
+    protected static String handleString(String value)
+    {
+        return ALLOWED_CHARS.matcher(value).matches() ? value : scapeString(value);
+    }
+
+    public static String scapeString(String value)
+    {
+        StringBuilder sb = new StringBuilder("\"");
+
+        for (int i = 0; i < value.length(); ++i)
+        {
+            char charAt = value.charAt(i);
+
+            if (charAt == '\\' || charAt == '"')
+            {
+                sb.append('\\');
+            }
+
+            sb.append(charAt);
+        }
+        return sb.append('"').toString();
+    }
 }
