--- ../src-base/minecraft/net/minecraft/nbt/NBTTagIntArray.java
+++ ../src-work/minecraft/net/minecraft/nbt/NBTTagIntArray.java
@@ -81,4 +81,19 @@
     {
         return this.intArray;
     }
+
+    @Override
+    public String crucible_toString() {
+        StringBuilder sb = new StringBuilder("[I;");
+        for (int i = 0; i < this.intArray.length; i++)
+        {
+            if (i != 0)
+            {
+                sb.append(',');
+            }
+
+            sb.append(this.intArray[i]);
+        }
+        return sb.append(']').toString();
+    }
 }
