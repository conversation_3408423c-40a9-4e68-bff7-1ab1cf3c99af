--- ../src-base/minecraft/net/minecraft/village/Village.java
+++ ../src-work/minecraft/net/minecraft/village/Village.java
@@ -71,7 +71,7 @@
             {
                 EntityIronGolem entityirongolem = new EntityIronGolem(this.worldObj);
                 entityirongolem.setPosition(vec3.xCoord, vec3.yCoord, vec3.zCoord);
-                this.worldObj.spawnEntityInWorld(entityirongolem);
+                this.worldObj.addEntity(entityirongolem, org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.VILLAGE_DEFENSE); // CraftBukkit
                 ++this.numIronGolems;
             }
         }
@@ -386,7 +386,9 @@
 
     private boolean isBlockDoor(int p_75574_1_, int p_75574_2_, int p_75574_3_)
     {
-        return this.worldObj.getBlock(p_75574_1_, p_75574_2_, p_75574_3_) == Blocks.wooden_door;
+        if (worldObj.blockExists(p_75574_1_, p_75574_2_, p_75574_3_)) {
+            return this.worldObj.getBlock(p_75574_1_, p_75574_2_, p_75574_3_) == Blocks.wooden_door;
+        } else return false;
     }
 
     private void updateVillageRadiusAndCenter()
