--- ../src-base/minecraft/net/minecraft/village/VillageSiege.java
+++ ../src-work/minecraft/net/minecraft/village/VillageSiege.java
@@ -196,7 +196,7 @@
             }
 
             entityzombie.setLocationAndAngles(vec3.xCoord, vec3.yCoord, vec3.zCoord, this.worldObj.rand.nextFloat() * 360.0F, 0.0F);
-            this.worldObj.spawnEntityInWorld(entityzombie);
+            this.worldObj.addEntity(entityzombie, org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.VILLAGE_INVASION); // CraftBukkit
             ChunkCoordinates chunkcoordinates = this.theVillage.getCenter();
             entityzombie.setHomeArea(chunkcoordinates.posX, chunkcoordinates.posY, chunkcoordinates.posZ, this.theVillage.getVillageRadius());
             return true;
