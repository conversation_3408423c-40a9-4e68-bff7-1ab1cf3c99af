--- ../src-base/minecraft/net/minecraft/tileentity/TileEntityChest.java
+++ ../src-work/minecraft/net/minecraft/tileentity/TileEntityChest.java
@@ -15,6 +15,13 @@
 import net.minecraft.nbt.NBTTagList;
 import net.minecraft.util.AxisAlignedBB;
 
+// CraftBukkit start
+import net.minecraft.init.Blocks;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.entity.HumanEntity;
+// CraftBukkit end
+
 public class TileEntityChest extends TileEntity implements IInventory
 {
     private ItemStack[] chestContents = new ItemStack[36];
@@ -31,6 +38,36 @@
     private String customName;
     private static final String __OBFID = "CL_00000346";
 
+    // CraftBukkit start
+    public List<HumanEntity> transaction = new java.util.ArrayList<HumanEntity>();
+    private int maxStack = MAX_STACK;
+
+    public ItemStack[] getContents()
+    {
+        return this.chestContents;
+    }
+
+    public void onOpen(CraftHumanEntity who)
+    {
+        transaction.add(who);
+    }
+
+    public void onClose(CraftHumanEntity who)
+    {
+        transaction.remove(who);
+    }
+
+    public List<HumanEntity> getViewers()
+    {
+        return transaction;
+    }
+
+    public void setMaxStackSize(int size)
+    {
+        maxStack = size;
+    }
+    // CraftBukkit end
+
     public TileEntityChest()
     {
         this.cachedChestType = -1;
@@ -296,6 +333,12 @@
     public void updateEntity()
     {
         super.updateEntity();
+
+        if (this.worldObj == null)
+        {
+            return;    // CraftBukkit
+        }
+
         this.checkForAdjacentChests();
         ++this.ticksSinceSync;
         float f;
@@ -410,8 +453,28 @@
             this.numPlayersUsing = 0;
         }
 
+        int oldPower = Math.max(0, Math.min(15, this.numPlayersUsing)); // CraftBukkit - Get power before new viewer is added
         ++this.numPlayersUsing;
+
+        if (this.worldObj == null)
+        {
+            return;    // CraftBukkit
+        }
+
         this.worldObj.addBlockEvent(this.xCoord, this.yCoord, this.zCoord, this.getBlockType(), 1, this.numPlayersUsing);
+
+        // CraftBukkit start - Call redstone event
+        if (this.getBlockType() == Blocks.trapped_chest)
+        {
+            int newPower = Math.max(0, Math.min(15, this.numPlayersUsing));
+
+            if (oldPower != newPower)
+            {
+                CraftEventFactory.callRedstoneChange(worldObj, this.xCoord, this.yCoord, this.zCoord, oldPower, newPower);
+            }
+        }
+
+        // CraftBukkit end
         this.worldObj.notifyBlocksOfNeighborChange(this.xCoord, this.yCoord, this.zCoord, this.getBlockType());
         this.worldObj.notifyBlocksOfNeighborChange(this.xCoord, this.yCoord - 1, this.zCoord, this.getBlockType());
     }
@@ -420,8 +483,28 @@
     {
         if (this.getBlockType() instanceof BlockChest)
         {
+            int oldPower = Math.max(0, Math.min(15, this.numPlayersUsing)); // CraftBukkit - Get power before new viewer is added
             --this.numPlayersUsing;
+
+            if (this.worldObj == null)
+            {
+                return;    // CraftBukkit
+            }
+
             this.worldObj.addBlockEvent(this.xCoord, this.yCoord, this.zCoord, this.getBlockType(), 1, this.numPlayersUsing);
+
+            // CraftBukkit start - Call redstone event
+            if (this.getBlockType() == Blocks.trapped_chest)
+            {
+                int newPower = Math.max(0, Math.min(15, this.numPlayersUsing));
+
+                if (oldPower != newPower)
+                {
+                    CraftEventFactory.callRedstoneChange(worldObj, this.xCoord, this.yCoord, this.zCoord, oldPower, newPower);
+                }
+            }
+
+            // CraftBukkit end
             this.worldObj.notifyBlocksOfNeighborChange(this.xCoord, this.yCoord, this.zCoord, this.getBlockType());
             this.worldObj.notifyBlocksOfNeighborChange(this.xCoord, this.yCoord - 1, this.zCoord, this.getBlockType());
         }
