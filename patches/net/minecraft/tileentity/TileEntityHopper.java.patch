--- ../src-base/minecraft/net/minecraft/tileentity/TileEntityHopper.java
+++ ../src-work/minecraft/net/minecraft/tileentity/TileEntityHopper.java
@@ -18,11 +18,54 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import net.minecraft.entity.item.EntityMinecartHopper;
+import net.minecraft.inventory.InventoryLargeChest;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftInventoryDoubleChest;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftItemStack;
+import org.bukkit.entity.HumanEntity;
+import org.bukkit.event.inventory.InventoryMoveItemEvent;
+import org.bukkit.event.inventory.InventoryPickupItemEvent;
+import org.bukkit.inventory.Inventory;
+import io.github.crucible.wrapper.ModInventoryWrapper;
+// CraftBukkit end
+
 public class TileEntityHopper extends TileEntity implements IHopper
 {
     private ItemStack[] field_145900_a = new ItemStack[5];
     private String field_145902_i;
     private int field_145901_j = -1;
+
+    // CraftBukkit start
+    public List<HumanEntity> transaction = new java.util.ArrayList<HumanEntity>();
+    private int maxStack = MAX_STACK;
+
+    public ItemStack[] getContents()
+    {
+        return this.field_145900_a;
+    }
+
+    public void onOpen(CraftHumanEntity who)
+    {
+        transaction.add(who);
+    }
+
+    public void onClose(CraftHumanEntity who)
+    {
+        transaction.remove(who);
+    }
+
+    public List<HumanEntity> getViewers()
+    {
+        return transaction;
+    }
+
+    public void setMaxStackSize(int size)
+    {
+        maxStack = size;
+    }
+    // CraftBukkit end
     private static final String __OBFID = "CL_00000359";
 
     public void readFromNBT(NBTTagCompound p_145839_1_)
@@ -212,12 +255,18 @@
 
                 if (flag)
                 {
-                    this.func_145896_c(8);
+                    this.func_145896_c(this.worldObj.getSpigotConfig().hopperTransfer); // Spigot // Cauldron
                     this.markDirty();
                     return true;
                 }
             }
 
+            // Spigot start
+            if (!this.func_145888_j())
+            {
+                this.func_145896_c(this.worldObj.getSpigotConfig().hopperCheck); // Cauldron
+            }
+            // Spigot end
             return false;
         }
         else
@@ -285,18 +334,70 @@
                     if (this.getStackInSlot(j) != null)
                     {
                         ItemStack itemstack = this.getStackInSlot(j).copy();
-                        ItemStack itemstack1 = func_145889_a(iinventory, this.decrStackSize(j, 1), i);
-
+                        // CraftBukkit start - Call event when pushing items into other inventories
+                        CraftItemStack oitemstack = CraftItemStack.asCraftMirror(this.decrStackSize(j, 1));
+                        Inventory destinationInventory;
+    
+                        // Have to special case large chests as they work oddly
+                        if (iinventory instanceof InventoryLargeChest)
+                        {
+                            destinationInventory = new CraftInventoryDoubleChest((InventoryLargeChest) iinventory);
+                        }
+                        else
+                        {
+                            // Cauldron start - support mod inventories, with no owners
+                            try {
+                                if (iinventory.getOwner() != null) {
+                                    destinationInventory = iinventory.getOwner().getInventory();
+                                } else {
+                                    // TODO: create a mod inventory for passing to the event, instead of null
+                                    destinationInventory = null;
+                                }
+                             } catch (AbstractMethodError e) { // fixes openblocks AbstractMethodError
+                                if (iinventory instanceof net.minecraft.tileentity.TileEntity) {
+                                    org.bukkit.inventory.InventoryHolder holder = net.minecraftforge.cauldron.CauldronUtils.getOwner((net.minecraft.tileentity.TileEntity)iinventory);
+                                    if (holder != null) {
+                                        destinationInventory = holder.getInventory();
+                                    } else {
+                                        destinationInventory = null;
+                                    }
+                                } else {
+                                    destinationInventory = null;
+                                }
+                            }
+                            // Cauldron end
+                        }
+    
+                        InventoryMoveItemEvent event = new InventoryMoveItemEvent(this.getOwner().getInventory(), oitemstack.clone(), destinationInventory, true);
+                        this.getWorldObj().getServer().getPluginManager().callEvent(event);
+    
+                        if (event.isCancelled())
+                        {
+                            this.setInventorySlotContents(j, itemstack);
+                            this.func_145896_c(worldObj.spigotConfig.hopperTransfer); // Spigot
+                            return false;
+                        }
+    
+                        ItemStack itemstack1 = func_145889_a(iinventory, CraftItemStack.asNMSCopy(event.getItem()), i);
+    
                         if (itemstack1 == null || itemstack1.stackSize == 0)
                         {
-                            iinventory.markDirty();
+                            if (event.getItem().equals(oitemstack))
+                            {
+                                iinventory.markDirty();
+                            }
+                            else
+                            {
+                                this.setInventorySlotContents(j, itemstack);
+                            }
+    
+                            // CraftBukkit end
                             return true;
                         }
-
+    
                         this.setInventorySlotContents(j, itemstack);
                     }
                 }
-
                 return false;
             }
         }
@@ -427,11 +528,71 @@
         if (itemstack != null && func_145890_b(p_145892_1_, itemstack, p_145892_2_, p_145892_3_))
         {
             ItemStack itemstack1 = itemstack.copy();
-            ItemStack itemstack2 = func_145889_a(p_145892_0_, p_145892_1_.decrStackSize(p_145892_2_, 1), -1);
+            // CraftBukkit start - Call event on collection of items from inventories into the hopper
+            CraftItemStack oitemstack = CraftItemStack.asCraftMirror(p_145892_1_.decrStackSize(p_145892_2_, 1));
+            Inventory sourceInventory = null;
 
+            // Have to special case large chests as they work oddly
+            if (p_145892_1_ instanceof InventoryLargeChest)
+            {
+                sourceInventory = new CraftInventoryDoubleChest((InventoryLargeChest) p_145892_1_);
+            }
+            else
+            {
+                // Cauldron start - support mod inventories, with no owners
+                try
+                {
+                    if (p_145892_1_.getOwner() != null)
+                    {
+                        sourceInventory = p_145892_1_.getOwner().getInventory();
+                    } 
+                    else
+                    {
+                        // TODO: create a mod inventory for passing to the event, instead of null
+                        sourceInventory = null;
+                    }
+                }
+                catch (AbstractMethodError e)
+                {
+                    sourceInventory = null;
+                }
+                finally { if (sourceInventory == null) { sourceInventory = new ModInventoryWrapper(p_145892_1_); } }
+                // Cauldron end
+            }
+
+            InventoryMoveItemEvent event = new InventoryMoveItemEvent(sourceInventory, oitemstack.clone(), p_145892_0_.getOwner().getInventory(), false);
+            p_145892_0_.getWorldObj().getServer().getPluginManager().callEvent(event);
+
+            if (event.isCancelled())
+            {
+                p_145892_1_.setInventorySlotContents(p_145892_2_, itemstack1);
+
+                if (p_145892_0_ instanceof TileEntityHopper)
+                {
+                    ((TileEntityHopper) p_145892_0_).func_145896_c(p_145892_0_.getWorldObj().spigotConfig.hopperTransfer); // Spigot
+                }
+                else if (p_145892_0_ instanceof EntityMinecartHopper)
+                {
+                    ((EntityMinecartHopper) p_145892_0_).setDisplayTileData(p_145892_0_.getWorldObj().spigotConfig.hopperTransfer / 2); // Spigot
+                }
+
+                return false;
+            }
+
+            ItemStack itemstack2 = func_145889_a(p_145892_0_, CraftItemStack.asNMSCopy(event.getItem()), -1);
+
             if (itemstack2 == null || itemstack2.stackSize == 0)
             {
-                p_145892_1_.markDirty();
+                if (event.getItem().equals(oitemstack))
+                {
+                    p_145892_1_.markDirty();
+                }
+                else
+                {
+                    p_145892_1_.setInventorySlotContents(p_145892_2_, itemstack1);
+                }
+
+                // CraftBukkit end
                 return true;
             }
 
@@ -451,6 +612,20 @@
         }
         else
         {
+            // CraftBukkit start
+            // Cauldron start - vanilla compatibility
+            if (p_145898_0_.getOwner() != null && p_145898_1_.getBukkitEntity() != null)
+            {
+                InventoryPickupItemEvent event = new InventoryPickupItemEvent(p_145898_0_.getOwner().getInventory(), (org.bukkit.entity.Item) p_145898_1_.getBukkitEntity());
+                p_145898_1_.worldObj.getServer().getPluginManager().callEvent(event);
+
+                if (event.isCancelled())
+                {
+                    return false;
+                }
+            }
+            // Cauldron end
+            // CraftBukkit end
             ItemStack itemstack = p_145898_1_.getEntityItem().copy();
             ItemStack itemstack1 = func_145889_a(p_145898_0_, itemstack, -1);
 
