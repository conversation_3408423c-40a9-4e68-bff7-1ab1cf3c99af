--- ../src-base/minecraft/net/minecraft/tileentity/TileEntityCommandBlock.java
+++ ../src-work/minecraft/net/minecraft/tileentity/TileEntityCommandBlock.java
@@ -12,39 +12,9 @@
 
 public class TileEntityCommandBlock extends TileEntity
 {
-    private final CommandBlockLogic field_145994_a = new CommandBlockLogic()
-    {
-        private static final String __OBFID = "CL_00000348";
-        public ChunkCoordinates getPlayerCoordinates()
-        {
-            return new ChunkCoordinates(TileEntityCommandBlock.this.xCoord, TileEntityCommandBlock.this.yCoord, TileEntityCommandBlock.this.zCoord);
-        }
-        public World getEntityWorld()
-        {
-            return TileEntityCommandBlock.this.getWorldObj();
-        }
-        public void func_145752_a(String p_145752_1_)
-        {
-            super.func_145752_a(p_145752_1_);
-            TileEntityCommandBlock.this.markDirty();
-        }
-        public void func_145756_e()
-        {
-            TileEntityCommandBlock.this.getWorldObj().markBlockForUpdate(TileEntityCommandBlock.this.xCoord, TileEntityCommandBlock.this.yCoord, TileEntityCommandBlock.this.zCoord);
-        }
-        @SideOnly(Side.CLIENT)
-        public int func_145751_f()
-        {
-            return 0;
-        }
-        @SideOnly(Side.CLIENT)
-        public void func_145757_a(ByteBuf p_145757_1_)
-        {
-            p_145757_1_.writeInt(TileEntityCommandBlock.this.xCoord);
-            p_145757_1_.writeInt(TileEntityCommandBlock.this.yCoord);
-            p_145757_1_.writeInt(TileEntityCommandBlock.this.zCoord);
-        }
-    };
+    private final TileEntityCommandBlockListener field_145994_a_CB = new TileEntityCommandBlockListener(this); // CraftBukkit
+    private final CommandBlockLogic field_145994_a = field_145994_a_CB; // Cauldron
+
     private static final String __OBFID = "CL_00000347";
 
     public void writeToNBT(NBTTagCompound p_145841_1_)
@@ -70,4 +40,12 @@
     {
         return this.field_145994_a;
     }
+
+    // Cauldron start
+    @Override
+    public boolean canUpdate()
+    {
+        return true; // Thermos - don't create incompat issues for no reason...jeez guys... >.< Fix #180
+    }
+    // Cauldron end
 }
