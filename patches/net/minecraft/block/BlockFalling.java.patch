--- ../src-base/minecraft/net/minecraft/block/BlockFalling.java
+++ ../src-work/minecraft/net/minecraft/block/BlockFalling.java
@@ -1,7 +1,7 @@
 package net.minecraft.block;
 
 import java.util.Random;
-import net.minecraft.block.material.Material;
+import net.minecraft.block.material.*;
 import net.minecraft.creativetab.CreativeTabs;
 import net.minecraft.entity.item.EntityFallingBlock;
 import net.minecraft.init.Blocks;
@@ -96,7 +96,7 @@
         {
             //TODO: <PERSON>, take a look here when doing liquids!
             Material material = block.blockMaterial;
-            return material == Material.water ? true : material == Material.lava;
+            return material == Material.water || material == Material.lava || material instanceof MaterialLiquid;
         }
     }
 
