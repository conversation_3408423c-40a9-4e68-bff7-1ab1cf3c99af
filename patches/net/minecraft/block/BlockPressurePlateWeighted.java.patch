--- ../src-base/minecraft/net/minecraft/block/BlockPressurePlateWeighted.java
+++ ../src-work/minecraft/net/minecraft/block/BlockPressurePlateWeighted.java
@@ -5,6 +5,12 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import net.minecraft.entity.player.EntityPlayer;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityInteractEvent;
+// CraftBukkit end
+
 public class BlockPressurePlateWeighted extends BlockBasePressurePlate
 {
     private final int field_150068_a;
@@ -18,18 +24,43 @@
 
     protected int func_150065_e(World p_150065_1_, int p_150065_2_, int p_150065_3_, int p_150065_4_)
     {
-        int l = Math.min(p_150065_1_.getEntitiesWithinAABB(Entity.class, this.func_150061_a(p_150065_2_, p_150065_3_, p_150065_4_)).size(), this.field_150068_a);
+        // CraftBukkit start
+        int l = 0;
+        java.util.Iterator iterator = p_150065_1_.getEntitiesWithinAABB(Entity.class, this.func_150061_a(p_150065_2_, p_150065_3_, p_150065_4_)).iterator();
 
-        if (l <= 0)
+        while (iterator.hasNext())
         {
-            return 0;
+            Entity entity = (Entity) iterator.next();
+            org.bukkit.event.Cancellable cancellable;
+
+            if (entity instanceof EntityPlayer)
+            {
+                cancellable = CraftEventFactory.callPlayerInteractEvent((EntityPlayer) entity, org.bukkit.event.block.Action.PHYSICAL, p_150065_2_, p_150065_3_, p_150065_4_, -1, null);
         }
         else
         {
+                cancellable = new EntityInteractEvent(entity.getBukkitEntity(), p_150065_1_.getWorld().getBlockAt(p_150065_2_, p_150065_3_, p_150065_4_));
+                p_150065_1_.getServer().getPluginManager().callEvent((EntityInteractEvent) cancellable);
+            }
+
+            // We only want to block turning the plate on if all events are cancelled
+            if (!cancellable.isCancelled())
+            {
+                l++;
+            }
+        }
+
+        l = Math.min(l, this.field_150068_a);
+        // CraftBukkit end
+
+        if (l <= 0)
+        {
+            return 0;
+        }
+
             float f = (float)Math.min(this.field_150068_a, l) / (float)this.field_150068_a;
             return MathHelper.ceiling_float_int(f * 15.0F);
         }
-    }
 
     protected int func_150060_c(int p_150060_1_)
     {
