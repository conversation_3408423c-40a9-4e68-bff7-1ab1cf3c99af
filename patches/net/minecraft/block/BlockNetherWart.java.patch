--- ../src-base/minecraft/net/minecraft/block/BlockNetherWart.java
+++ ../src-work/minecraft/net/minecraft/block/BlockNetherWart.java
@@ -12,7 +12,7 @@
 import net.minecraft.item.ItemStack;
 import net.minecraft.util.IIcon;
 import net.minecraft.world.World;
-import net.minecraftforge.common.util.ForgeDirection;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class BlockNetherWart extends BlockBush
 {
@@ -45,7 +45,7 @@
         if (l < 3 && p_149674_5_.nextInt(10) == 0)
         {
             ++l;
-            p_149674_1_.setBlockMetadataWithNotify(p_149674_2_, p_149674_3_, p_149674_4_, l, 2);
+            CraftEventFactory.handleBlockGrowEvent(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, this, l); // CraftBukkit
         }
 
         super.updateTick(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, p_149674_5_);
