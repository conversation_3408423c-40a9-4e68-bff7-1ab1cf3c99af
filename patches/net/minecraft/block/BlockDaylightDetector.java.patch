--- ../src-base/minecraft/net/minecraft/block/BlockDaylightDetector.java
+++ ../src-work/minecraft/net/minecraft/block/BlockDaylightDetector.java
@@ -13,6 +13,7 @@
 import net.minecraft.world.EnumSkyBlock;
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class BlockDaylightDetector extends BlockContainer
 {
@@ -73,6 +74,7 @@
 
             if (l != i1)
             {
+                i1 = CraftEventFactory.callRedstoneChange(p_149957_1_, p_149957_2_, p_149957_3_, p_149957_4_, l, i1).getNewCurrent(); // CraftBukkit - Call BlockRedstoneEvent
                 p_149957_1_.setBlockMetadataWithNotify(p_149957_2_, p_149957_3_, p_149957_4_, i1, 3);
             }
         }
