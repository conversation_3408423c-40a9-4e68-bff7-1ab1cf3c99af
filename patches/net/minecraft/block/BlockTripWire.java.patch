--- ../src-base/minecraft/net/minecraft/block/BlockTripWire.java
+++ ../src-work/minecraft/net/minecraft/block/BlockTripWire.java
@@ -16,6 +16,9 @@
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityInteractEvent; // CraftBukkit
+
 public class BlockTripWire extends Block
 {
     private static final String __OBFID = "CL_00000328";
@@ -208,6 +211,51 @@
             }
         }
 
+        // CraftBukkit start - Call interact even when triggering connected tripwire
+        if (flag != flag1 && flag1 && (p_150140_1_.getBlockMetadata(p_150140_2_, p_150140_3_, p_150140_4_) & 4) == 4)
+        {
+            org.bukkit.World bworld = p_150140_1_.getWorld();
+            org.bukkit.plugin.PluginManager manager = p_150140_1_.getServer().getPluginManager();
+            org.bukkit.block.Block block = bworld.getBlockAt(p_150140_2_, p_150140_3_, p_150140_4_);
+            boolean allowed = false;
+
+            // If all of the events are cancelled block the tripwire trigger, else allow
+            for (Object object : list)
+            {
+                if (object != null)
+                {
+                    org.bukkit.event.Cancellable cancellable;
+
+                    if (object instanceof EntityPlayer)
+                    {
+                        cancellable = CraftEventFactory.callPlayerInteractEvent((EntityPlayer) object, org.bukkit.event.block.Action.PHYSICAL, p_150140_2_, p_150140_3_, p_150140_4_, -1, null);
+                    }
+                    else if (object instanceof Entity)
+                    {
+                        cancellable = new EntityInteractEvent(((Entity) object).getBukkitEntity(), block);
+                        manager.callEvent((EntityInteractEvent) cancellable);
+                    }
+                    else
+                    {
+                        continue;
+                    }
+
+                    if (!cancellable.isCancelled())
+                    {
+                        allowed = true;
+                        break;
+                    }
+                }
+            }
+
+            if (!allowed)
+            {
+                return;
+            }
+        }
+
+        // CraftBukkit end
+
         if (flag1 && !flag)
         {
             l |= 1;
