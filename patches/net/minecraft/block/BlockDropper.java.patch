--- ../src-base/minecraft/net/minecraft/block/BlockDropper.java
+++ ../src-work/minecraft/net/minecraft/block/BlockDropper.java
@@ -13,6 +13,12 @@
 import net.minecraft.tileentity.TileEntityHopper;
 import net.minecraft.util.Facing;
 import net.minecraft.world.World;
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftInventoryDoubleChest;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftItemStack;
+import org.bukkit.event.inventory.InventoryMoveItemEvent;
+import net.minecraft.inventory.InventoryLargeChest;
+// CraftBukkit end
 
 public class BlockDropper extends BlockDispenser
 {
@@ -38,7 +44,7 @@
         return new TileEntityDropper();
     }
 
-    protected void func_149941_e(World p_149941_1_, int p_149941_2_, int p_149941_3_, int p_149941_4_)
+    public void func_149941_e(World p_149941_1_, int p_149941_2_, int p_149941_3_, int p_149941_4_)   // CraftBukkit - protected -> public
     {
         BlockSourceImpl blocksourceimpl = new BlockSourceImpl(p_149941_1_, p_149941_2_, p_149941_3_, p_149941_4_);
         TileEntityDispenser tileentitydispenser = (TileEntityDispenser)blocksourceimpl.getBlockTileEntity();
@@ -60,10 +66,53 @@
 
                 if (iinventory != null)
                 {
-                    itemstack1 = TileEntityHopper.func_145889_a(iinventory, itemstack.copy().splitStack(1), Facing.oppositeSide[i1]);
+                    // CraftBukkit start - Fire event when pushing items into other inventories
+                    CraftItemStack oitemstack = CraftItemStack.asCraftMirror(itemstack.copy().splitStack(1));
+                    org.bukkit.inventory.Inventory destinationInventory;
 
-                    if (itemstack1 == null)
+                    // Have to special case large chests as they work oddly
+                    if (iinventory instanceof InventoryLargeChest)
                     {
+                        destinationInventory = new CraftInventoryDoubleChest((InventoryLargeChest) iinventory);
+                    }
+                    else {
+                        // Crucible start - support mod inventories, with no owners
+                        try {
+
+                            if (iinventory.getOwner().getInventory() != null) {
+                                destinationInventory = iinventory.getOwner().getInventory();
+                            } else {
+                                // TODO: create a mod inventory for passing to the event, instead of null
+                                destinationInventory = null;
+                            }
+                        } catch (AbstractMethodError e) { // fixes openblocks AbstractMethodError
+                            if (iinventory instanceof TileEntity) {
+                                org.bukkit.inventory.InventoryHolder holder = net.minecraftforge.cauldron.CauldronUtils.getOwner((net.minecraft.tileentity.TileEntity) iinventory);
+                                if (holder != null) {
+                                    destinationInventory = holder.getInventory();
+                                } else {
+                                    destinationInventory = null;
+                                }
+                            } else {
+                                destinationInventory = null;
+                            }
+                        }
+                        // Crucible end
+                    }
+
+                    InventoryMoveItemEvent event = new InventoryMoveItemEvent(tileentitydispenser.getOwner().getInventory(), oitemstack.clone(), destinationInventory, true);
+                    p_149941_1_.getServer().getPluginManager().callEvent(event);
+
+                    if (event.isCancelled())
+                    {
+                        return;
+                    }
+
+                    itemstack1 = TileEntityHopper.func_145889_a(iinventory, CraftItemStack.asNMSCopy(event.getItem()), Facing.oppositeSide[i1]);
+
+                    if (event.getItem().equals(oitemstack) && itemstack1 == null)
+                    {
+                        // CraftBukkit end
                         itemstack1 = itemstack.copy();
 
                         if (--itemstack1.stackSize == 0)
