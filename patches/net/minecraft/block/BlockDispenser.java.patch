--- ../src-base/minecraft/net/minecraft/block/BlockDispenser.java
+++ ../src-work/minecraft/net/minecraft/block/BlockDispenser.java
@@ -30,6 +30,7 @@
 {
     public static final IRegistry dispenseBehaviorRegistry = new RegistryDefaulted(new BehaviorDefaultDispenseItem());
     protected Random field_149942_b = new Random();
+    public static boolean eventFired = false; // CraftBukkit
     @SideOnly(Side.CLIENT)
     protected IIcon field_149944_M;
     @SideOnly(Side.CLIENT)
@@ -124,7 +125,8 @@
         }
     }
 
-    protected void func_149941_e(World p_149941_1_, int p_149941_2_, int p_149941_3_, int p_149941_4_)
+    // CraftBukkit - protected -> public
+    public void func_149941_e(World p_149941_1_, int p_149941_2_, int p_149941_3_, int p_149941_4_)
     {
         BlockSourceImpl blocksourceimpl = new BlockSourceImpl(p_149941_1_, p_149941_2_, p_149941_3_, p_149941_4_);
         TileEntityDispenser tileentitydispenser = (TileEntityDispenser)blocksourceimpl.getBlockTileEntity();
@@ -145,6 +147,7 @@
                 if (ibehaviordispenseitem != IBehaviorDispenseItem.itemDispenseBehaviorProvider)
                 {
                     ItemStack itemstack1 = ibehaviordispenseitem.dispense(blocksourceimpl, itemstack);
+                    eventFired = false; // CraftBukkit - reset event status
                     tileentitydispenser.setInventorySlotContents(l, itemstack1.stackSize == 0 ? null : itemstack1);
                 }
             }
