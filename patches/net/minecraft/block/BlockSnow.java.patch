--- ../src-base/minecraft/net/minecraft/block/BlockSnow.java
+++ ../src-work/minecraft/net/minecraft/block/BlockSnow.java
@@ -10,12 +10,11 @@
 import net.minecraft.init.Blocks;
 import net.minecraft.init.Items;
 import net.minecraft.item.Item;
-import net.minecraft.item.ItemStack;
-import net.minecraft.stats.StatList;
 import net.minecraft.util.AxisAlignedBB;
 import net.minecraft.world.EnumSkyBlock;
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class BlockSnow extends Block
 {
@@ -114,6 +113,14 @@
     {
         if (p_149674_1_.getSavedLightValue(EnumSkyBlock.Block, p_149674_2_, p_149674_3_, p_149674_4_) > 11)
         {
+            // CraftBukkit start
+            if (CraftEventFactory.callBlockFadeEvent(p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), Blocks.air).isCancelled())
+            {
+                return;
+            }
+
+            // CraftBukkit end
+            this.dropBlockAsItem(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, p_149674_1_.getBlockMetadata(p_149674_2_, p_149674_3_, p_149674_4_), 0);
             p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_);
         }
     }
