--- ../src-base/minecraft/net/minecraft/block/BlockTripWireHook.java
+++ ../src-work/minecraft/net/minecraft/block/BlockTripWireHook.java
@@ -12,6 +12,8 @@
 import net.minecraftforge.common.util.ForgeDirection;
 import static net.minecraftforge.common.util.ForgeDirection.*;
 
+import org.bukkit.event.block.BlockRedstoneEvent; // CraftBukkit
+
 public class BlockTripWireHook extends Block
 {
     private static final String __OBFID = "CL_00000329";
@@ -210,6 +212,17 @@
             this.func_150135_a(p_150136_1_, l2, p_150136_3_, i3, flag4, flag5, flag2, flag3);
         }
 
+        // CraftBukkit start
+        org.bukkit.block.Block block = p_150136_1_.getWorld().getBlockAt(p_150136_2_, p_150136_3_, p_150136_4_);
+        BlockRedstoneEvent eventRedstone = new BlockRedstoneEvent(block, 15, 0);
+        p_150136_1_.getServer().getPluginManager().callEvent(eventRedstone);
+
+        if (eventRedstone.getNewCurrent() > 0)
+        {
+            return;
+        }
+
+        // CraftBukkit end
         this.func_150135_a(p_150136_1_, p_150136_2_, p_150136_3_, p_150136_4_, flag4, flag5, flag2, flag3);
 
         if (!p_150136_5_)
