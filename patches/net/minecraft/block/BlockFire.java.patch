--- ../src-base/minecraft/net/minecraft/block/BlockFire.java
+++ ../src-work/minecraft/net/minecraft/block/BlockFire.java
@@ -2,10 +2,15 @@
 
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
+
+import java.util.HashMap;
 import java.util.IdentityHashMap;
 import java.util.Map.Entry;
 import java.util.Random;
+import java.util.WeakHashMap;
+
 import com.google.common.collect.Maps;
+import io.github.crucible.CrucibleConfigs;
 import net.minecraft.block.material.MapColor;
 import net.minecraft.block.material.Material;
 import net.minecraft.client.renderer.texture.IIconRegister;
@@ -14,9 +19,14 @@
 import net.minecraft.util.IIcon;
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
-import net.minecraft.world.WorldProviderEnd;
 import net.minecraftforge.common.util.ForgeDirection;
 import static net.minecraftforge.common.util.ForgeDirection.*;
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.block.CraftBlock;
+import org.bukkit.event.block.BlockBurnEvent;
+import org.bukkit.event.block.BlockSpreadEvent;
+// CraftBukkit end
 
 public class BlockFire extends Block
 {
@@ -105,12 +115,12 @@
 
             if (!this.canPlaceBlockAt(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_))
             {
-                p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_);
+                if (!CraftEventFactory.callBlockFadeEvent(p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), Blocks.air).isCancelled()) { p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_); } // CraftBukkit - invalid place location
             }
 
             if (!flag && p_149674_1_.isRaining() && (p_149674_1_.canLightningStrikeAt(p_149674_2_, p_149674_3_, p_149674_4_) || p_149674_1_.canLightningStrikeAt(p_149674_2_ - 1, p_149674_3_, p_149674_4_) || p_149674_1_.canLightningStrikeAt(p_149674_2_ + 1, p_149674_3_, p_149674_4_) || p_149674_1_.canLightningStrikeAt(p_149674_2_, p_149674_3_, p_149674_4_ - 1) || p_149674_1_.canLightningStrikeAt(p_149674_2_, p_149674_3_, p_149674_4_ + 1)))
             {
-                p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_);
+                if (!CraftEventFactory.callBlockFadeEvent(p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), Blocks.air).isCancelled()) { p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_); } // CraftBukkit - extinguished by rain
             }
             else
             {
@@ -127,12 +137,12 @@
                 {
                     if (!World.doesBlockHaveSolidTopSurface(p_149674_1_, p_149674_2_, p_149674_3_ - 1, p_149674_4_) || l > 3)
                     {
-                        p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_);
+                        if (!CraftEventFactory.callBlockFadeEvent(p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), Blocks.air).isCancelled()) { p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_); } // CraftBukkit - burn out of inflammable block
                     }
                 }
                 else if (!flag && !this.canCatchFire(p_149674_1_, p_149674_2_, p_149674_3_ - 1, p_149674_4_, UP) && l == 15 && p_149674_5_.nextInt(4) == 0)
                 {
-                    p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_);
+                    if (!CraftEventFactory.callBlockFadeEvent(p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), Blocks.air).isCancelled()) { p_149674_1_.setBlockToAir(p_149674_2_, p_149674_3_, p_149674_4_); } // CraftBukkit - burn out
                 }
                 else
                 {
@@ -186,7 +196,30 @@
                                                 k2 = 15;
                                             }
 
-                                            p_149674_1_.setBlock(i1, k1, j1, this, k2, 3);
+                                            // CraftBukkit start - Call to stop spread of fire
+                                            if (p_149674_1_.getBlock(i1, k1, j1) != Blocks.fire)
+                                            {
+                                                if (CraftEventFactory.callBlockIgniteEvent(p_149674_1_, i1, k1, j1, p_149674_2_, p_149674_3_, p_149674_4_).isCancelled())
+                                                {
+                                                    continue;
+                                                }
+
+                                                org.bukkit.Server server = p_149674_1_.getServer();
+                                                org.bukkit.World bworld = p_149674_1_.getWorld();
+                                                org.bukkit.block.BlockState blockState = bworld.getBlockAt(i1, k1, j1).getState();
+                                                blockState.setTypeId(Block.getIdFromBlock(this));
+                                                blockState.setData(new org.bukkit.material.MaterialData(Block.getIdFromBlock(this), (byte) k2));
+                                                BlockSpreadEvent spreadEvent = new BlockSpreadEvent(blockState.getBlock(), bworld.getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_), blockState);
+                                                server.getPluginManager().callEvent(spreadEvent);
+
+                                                if (!spreadEvent.isCancelled())
+                                                {
+                                                    p_149674_1_.setBlock(i1, k1, j1, ((CraftBlock)blockState.getBlock()).getNMSBlock(), blockState.getRawData(), 3); // KCauldron - DragonAPI uses this call
+                                                    blockState.update(true);
+                                                }
+                                            }
+
+                                            // CraftBukkit end
                                         }
                                     }
                                 }
@@ -209,14 +242,50 @@
         this.tryCatchFire(p_149841_1_, p_149841_2_, p_149841_3_, p_149841_4_, p_149841_5_, p_149841_6_, p_149841_7_, UP);
     }
 
+    private final class pXYZ { 
+    	private int x, z;
+    	private byte y; 
+    	public pXYZ(int x, int y, int z){ this.x = x; this.y = (byte) (y - 128); this.z = z; }
+        public int hashCode()
+        {
+        	return ((y * 31 + x) * 31 + z) * 17 + y;
+        }
+    }
+    private static WeakHashMap<World,HashMap<pXYZ,Long>> failures = new WeakHashMap<World, HashMap<pXYZ,Long>>();
     private void tryCatchFire(World p_149841_1_, int p_149841_2_, int p_149841_3_, int p_149841_4_, int p_149841_5_, Random p_149841_6_, int p_149841_7_, ForgeDirection face)
     {
         int j1 = p_149841_1_.getBlock(p_149841_2_, p_149841_3_, p_149841_4_).getFlammability(p_149841_1_, p_149841_2_, p_149841_3_, p_149841_4_, face);
-
-        if (p_149841_6_.nextInt(p_149841_5_) < j1)
+        
+        if (!p_149841_1_.getGameRules().getGameRuleBooleanValue("doFireTick"))
         {
+        	return; // Thermos don't let things catch fire if doFireTick is off
+        }
+        else if (p_149841_6_.nextInt(p_149841_5_) < j1)
+        {
+        	HashMap<pXYZ,Long> lavie = failures.get(p_149841_1_);
+        	if (lavie == null) { lavie = new HashMap<pXYZ,Long>(); failures.put(p_149841_1_, lavie); }
+        	pXYZ lapoint = new pXYZ(p_149841_2_, p_149841_3_, p_149841_4_);
+        	Long last = lavie.get(lapoint);
+        	long now = System.currentTimeMillis();
+        	if (last != null && now - last < 1000)
+        	{
+        		return; //Thermos premature ending
+        	}
+        	
             boolean flag = p_149841_1_.getBlock(p_149841_2_, p_149841_3_, p_149841_4_) == Blocks.tnt;
+            // CraftBukkit start
+            org.bukkit.block.Block theBlock = p_149841_1_.getWorld().getBlockAt(p_149841_2_, p_149841_3_, p_149841_4_);
+            BlockBurnEvent event = new BlockBurnEvent(theBlock);
+            p_149841_1_.getServer().getPluginManager().callEvent(event);
 
+            if (event.isCancelled())
+            {
+            	lavie.put(lapoint, System.currentTimeMillis());
+                return;
+            }
+
+            // CraftBukkit end
+
             if (p_149841_6_.nextInt(p_149841_7_ + 10) < 5 && !p_149841_1_.canLightningStrikeAt(p_149841_2_, p_149841_3_, p_149841_4_))
             {
                 int k1 = p_149841_7_ + p_149841_6_.nextInt(5) / 4;
@@ -297,17 +366,17 @@
     {
         if (!World.doesBlockHaveSolidTopSurface(p_149695_1_, p_149695_2_, p_149695_3_ - 1, p_149695_4_) && !this.canNeighborBurn(p_149695_1_, p_149695_2_, p_149695_3_, p_149695_4_))
         {
-            p_149695_1_.setBlockToAir(p_149695_2_, p_149695_3_, p_149695_4_);
+            if (!CraftEventFactory.callBlockFadeEvent(p_149695_1_.getWorld().getBlockAt(p_149695_2_, p_149695_3_, p_149695_4_), Blocks.air).isCancelled()) { p_149695_1_.setBlockToAir(p_149695_2_, p_149695_3_, p_149695_4_); } // CraftBukkit - fuel block gone
         }
     }
 
     public void onBlockAdded(World p_149726_1_, int p_149726_2_, int p_149726_3_, int p_149726_4_)
     {
-        if (p_149726_1_.provider.dimensionId > 0 || !Blocks.portal.func_150000_e(p_149726_1_, p_149726_2_, p_149726_3_, p_149726_4_))
+        if (!CrucibleConfigs.configs.thermos_allowNetherPortal && p_149726_1_.provider.dimensionId > 0 || !Blocks.portal.func_150000_e(p_149726_1_, p_149726_2_, p_149726_3_, p_149726_4_))
         {
             if (!World.doesBlockHaveSolidTopSurface(p_149726_1_, p_149726_2_, p_149726_3_ - 1, p_149726_4_) && !this.canNeighborBurn(p_149726_1_, p_149726_2_, p_149726_3_, p_149726_4_))
             {
-                p_149726_1_.setBlockToAir(p_149726_2_, p_149726_3_, p_149726_4_);
+                if (!CraftEventFactory.callBlockFadeEvent(p_149726_1_.getWorld().getBlockAt(p_149726_2_, p_149726_3_, p_149726_4_), Blocks.air).isCancelled()) { p_149726_1_.setBlockToAir(p_149726_2_, p_149726_3_, p_149726_4_); } // CraftBukkit - fuel block broke
             }
             else
             {
@@ -513,7 +582,8 @@
      */
     public int getChanceToEncourageFire(IBlockAccess world, int x, int y, int z, int oldChance, ForgeDirection face)
     {
-        int newChance = world.getBlock(x, y, z).getFireSpreadSpeed(world, x, y, z, face);
+    	Block block = world.getBlock(x, y, z);
+        int newChance = block != null ? getFireSpreadSpeed(world, x, y, z, face) : 0;
         return (newChance > oldChance ? newChance : oldChance);
     }
     /*================================= Forge Start ======================================*/
