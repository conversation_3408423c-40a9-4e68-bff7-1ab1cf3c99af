--- ../src-base/minecraft/net/minecraft/block/Block.java
+++ ../src-work/minecraft/net/minecraft/block/Block.java
@@ -27,6 +27,7 @@
 import net.minecraft.init.Blocks;
 import net.minecraft.item.Item;
 import net.minecraft.item.ItemBlock;
+import net.minecraft.item.ItemShears;
 import net.minecraft.item.ItemStack;
 import net.minecraft.stats.StatList;
 import net.minecraft.tileentity.TileEntity;
@@ -46,6 +47,7 @@
 import net.minecraftforge.common.EnumPlantType;
 import net.minecraftforge.common.ForgeHooks;
 import net.minecraftforge.common.IPlantable;
+import net.minecraftforge.common.IShearable;
 import net.minecraftforge.common.MinecraftForge;
 import net.minecraftforge.common.util.ForgeDirection;
 import net.minecraftforge.common.util.RotationHelper;
@@ -122,6 +124,16 @@
     private String unlocalizedName;
     @SideOnly(Side.CLIENT)
     protected IIcon blockIcon;
+    public boolean isForgeBlock; // Cauldron
+    // Paper start
+    public co.aikar.timings.Timing timing;
+    public co.aikar.timings.Timing getTiming() {
+        if (timing == null) {
+            timing = co.aikar.timings.MinecraftTimings.getBlockTiming(this);
+        }
+        return timing;
+    }
+    // Paper end
     private static final String __OBFID = "CL_00000199";
 
     public final cpw.mods.fml.common.registry.RegistryDelegate<Block> delegate = 
@@ -133,6 +145,8 @@
 
     public static Block getBlockById(int p_149729_0_)
     {
+    	if(p_149729_0_ == 0) return Blocks.air;
+    	
         Block ret = (Block)blockRegistry.getObjectById(p_149729_0_);
         return ret == null ? Blocks.air : ret;
     }
@@ -564,6 +578,10 @@
 
     public void addCollisionBoxesToList(World p_149743_1_, int p_149743_2_, int p_149743_3_, int p_149743_4_, AxisAlignedBB p_149743_5_, List p_149743_6_, Entity p_149743_7_)
     {
+    	if(p_149743_6_ instanceof ArrayList)
+    	{
+    		((ArrayList)p_149743_6_).ensureCapacity(30); // Thermos - force ArrayList minimum size
+    	}
         AxisAlignedBB axisalignedbb1 = this.getCollisionBoundingBoxFromPool(p_149743_1_, p_149743_2_, p_149743_3_, p_149743_4_);
 
         if (axisalignedbb1 != null && p_149743_5_.intersectsWith(axisalignedbb1))
@@ -954,13 +972,18 @@
         if (this.canSilkHarvest(p_149636_1_, p_149636_2_, p_149636_3_, p_149636_4_, p_149636_5_, p_149636_6_) && EnchantmentHelper.getSilkTouchModifier(p_149636_2_))
         {
             ArrayList<ItemStack> items = new ArrayList<ItemStack>();
-            ItemStack itemstack = this.createStackedBlock(p_149636_6_);
-
-            if (itemstack != null)
+            
+            if (!((p_149636_2_.getHeldItem().getItem() instanceof ItemShears) && (this instanceof BlockLeavesBase) && (this instanceof IShearable)))
             {
-                items.add(itemstack);
+                ItemStack itemstack = this.createStackedBlock(p_149636_6_);
+
+                if (itemstack != null)
+                {
+                    items.add(itemstack);
+                }            	
             }
 
+
             ForgeEventFactory.fireBlockHarvesting(items, p_149636_1_, this, p_149636_3_, p_149636_4_, p_149636_5_, p_149636_6_, 0, 1.0f, true, p_149636_2_);
             for (ItemStack is : items)
             {
@@ -1131,6 +1154,23 @@
         return this;
     }
 
+    // Spigot start
+    public static float range(float min, float value, float max)
+    {
+        if (value < min)
+        {
+            return min;
+        }
+
+        if (value > max)
+        {
+            return max;
+        }
+
+        return value;
+    }
+    // Spigot end
+
     @SideOnly(Side.CLIENT)
     protected String getTextureName()
     {
