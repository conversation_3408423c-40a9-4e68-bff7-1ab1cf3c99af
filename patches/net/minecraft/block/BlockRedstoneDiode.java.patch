--- ../src-base/minecraft/net/minecraft/block/BlockRedstoneDiode.java
+++ ../src-work/minecraft/net/minecraft/block/BlockRedstoneDiode.java
@@ -3,6 +3,8 @@
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
 import java.util.Random;
+
+import io.github.crucible.CrucibleConfigs;
 import net.minecraft.block.material.Material;
 import net.minecraft.entity.EntityLivingBase;
 import net.minecraft.init.Blocks;
@@ -13,16 +15,21 @@
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory; // CraftBukkit
+
+
 public abstract class BlockRedstoneDiode extends BlockDirectional
 {
     protected final boolean isRepeaterPowered;
     private static final String __OBFID = "CL_00000226";
+    private boolean limit;
 
     protected BlockRedstoneDiode(boolean p_i45400_1_)
     {
         super(Material.circuits);
         this.isRepeaterPowered = p_i45400_1_;
         this.setBlockBounds(0.0F, 0.0F, 0.0F, 1.0F, 0.125F, 1.0F);
+        if(this.getClass().getSimpleName().toLowerCase().contains("redstonerepeater"))limit = true;
     }
 
     public boolean renderAsNormalBlock()
@@ -40,22 +47,37 @@
         return !World.doesBlockHaveSolidTopSurface(p_149718_1_, p_149718_2_, p_149718_3_ - 1, p_149718_4_) ? false : super.canBlockStay(p_149718_1_, p_149718_2_, p_149718_3_, p_149718_4_);
     }
 
+    private long last = 0L;
     public void updateTick(World p_149674_1_, int p_149674_2_, int p_149674_3_, int p_149674_4_, Random p_149674_5_)
     {
+        if (System.currentTimeMillis() - last <= CrucibleConfigs.configs.cauldron_optimization_redstoneRepeaterUpdateSpeed) { return; }
+        last = System.currentTimeMillis();
         int l = p_149674_1_.getBlockMetadata(p_149674_2_, p_149674_3_, p_149674_4_);
 
-        if (!this.func_149910_g(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, l))
+        if (!this.func_149910_g((IBlockAccess) p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, l))   // CraftBukkit - Cast world to IBlockAccess to call the right method.
         {
             boolean flag = this.isGettingInput(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, l);
 
             if (this.isRepeaterPowered && !flag)
             {
+                // CraftBukkit start
+                if (CraftEventFactory.callRedstoneChange(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, 15, 0).getNewCurrent() != 0)
+                {
+                    return;
+                }
+
+                // CraftBukkit end
                 p_149674_1_.setBlock(p_149674_2_, p_149674_3_, p_149674_4_, this.getBlockUnpowered(), l, 2);
             }
             else if (!this.isRepeaterPowered)
             {
+                // CraftBukkit start
+                if (CraftEventFactory.callRedstoneChange(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, 0, 15).getNewCurrent() != 15)
+                {
+                    return;
+                }
+                // CraftBukkit end
                 p_149674_1_.setBlock(p_149674_2_, p_149674_3_, p_149674_4_, this.getBlockPowered(), l, 2);
-
                 if (!flag)
                 {
                     p_149674_1_.scheduleBlockUpdateWithPriority(p_149674_2_, p_149674_3_, p_149674_4_, this.getBlockPowered(), this.func_149899_k(l), -1);
