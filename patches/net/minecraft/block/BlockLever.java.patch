--- ../src-base/minecraft/net/minecraft/block/BlockLever.java
+++ ../src-work/minecraft/net/minecraft/block/BlockLever.java
@@ -13,6 +13,8 @@
 import net.minecraftforge.common.util.ForgeDirection;
 import static net.minecraftforge.common.util.ForgeDirection.*;
 
+import org.bukkit.event.block.BlockRedstoneEvent; // CraftBukkit
+
 public class BlockLever extends Block
 {
     private static final String __OBFID = "CL_00000264";
@@ -270,6 +272,19 @@
             int i1 = p_149727_1_.getBlockMetadata(p_149727_2_, p_149727_3_, p_149727_4_);
             int j1 = i1 & 7;
             int k1 = 8 - (i1 & 8);
+            // CraftBukkit start - Interact Lever
+            org.bukkit.block.Block block = p_149727_1_.getWorld().getBlockAt(p_149727_2_, p_149727_3_, p_149727_4_);
+            int old = (k1 != 8) ? 15 : 0;
+            int current = (k1 == 8) ? 15 : 0;
+            BlockRedstoneEvent eventRedstone = new BlockRedstoneEvent(block, old, current);
+            p_149727_1_.getServer().getPluginManager().callEvent(eventRedstone);
+
+            if ((eventRedstone.getNewCurrent() > 0) != (k1 == 8))
+            {
+                return true;
+            }
+
+            // CraftBukkit end
             p_149727_1_.setBlockMetadataWithNotify(p_149727_2_, p_149727_3_, p_149727_4_, j1 + k1, 3);
             p_149727_1_.playSoundEffect((double)p_149727_2_ + 0.5D, (double)p_149727_3_ + 0.5D, (double)p_149727_4_ + 0.5D, "random.click", 0.3F, k1 > 0 ? 0.6F : 0.5F);
             p_149727_1_.notifyBlocksOfNeighborChange(p_149727_2_, p_149727_3_, p_149727_4_, this);
