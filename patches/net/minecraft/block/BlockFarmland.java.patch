--- ../src-base/minecraft/net/minecraft/block/BlockFarmland.java
+++ ../src-work/minecraft/net/minecraft/block/BlockFarmland.java
@@ -15,6 +15,11 @@
 import net.minecraftforge.common.IPlantable;
 import net.minecraftforge.common.util.ForgeDirection;
 
+// CraftBukkit start
+import org.bukkit.event.entity.EntityInteractEvent;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+// CraftBukkit end
+
 public class BlockFarmland extends Block
 {
     @SideOnly(Side.CLIENT)
@@ -64,6 +69,15 @@
             }
             else if (!this.func_149822_e(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_))
             {
+                // CraftBukkit start
+                org.bukkit.block.Block block = p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_);
+
+                if (CraftEventFactory.callBlockFadeEvent(block, Blocks.dirt).isCancelled())
+                {
+                    return;
+                }
+
+                // CraftBukkit end
                 p_149674_1_.setBlock(p_149674_2_, p_149674_3_, p_149674_4_, Blocks.dirt);
             }
         }
@@ -82,6 +96,26 @@
                 return;
             }
 
+            // CraftBukkit start - Interact soil
+            // Cauldron start - validate data before sending event
+            org.bukkit.event.Cancellable cancellable = null;
+
+            if (p_149746_5_ instanceof EntityPlayer)
+            {
+                cancellable = CraftEventFactory.callPlayerInteractEvent((EntityPlayer) p_149746_5_, org.bukkit.event.block.Action.PHYSICAL, p_149746_2_, p_149746_3_, p_149746_4_, -1, null);
+            }
+            else if (p_149746_1_.getWorld() != null && p_149746_5_ != null)
+            {
+                cancellable = new EntityInteractEvent(p_149746_5_.getBukkitEntity(), p_149746_1_.getWorld().getBlockAt(p_149746_2_, p_149746_3_, p_149746_4_));
+                p_149746_1_.getServer().getPluginManager().callEvent((EntityInteractEvent) cancellable);
+            }
+
+            if (cancellable != null && cancellable.isCancelled())
+            {
+                return;
+            }
+            // Cauldron end
+            // CraftBukkit end
             p_149746_1_.setBlock(p_149746_2_, p_149746_3_, p_149746_4_, Blocks.dirt);
         }
     }
