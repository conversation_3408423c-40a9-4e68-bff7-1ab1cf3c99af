--- ../src-base/minecraft/net/minecraft/block/BlockCommandBlock.java
+++ ../src-work/minecraft/net/minecraft/block/BlockCommandBlock.java
@@ -10,6 +10,8 @@
 import net.minecraft.tileentity.TileEntityCommandBlock;
 import net.minecraft.world.World;
 
+import org.bukkit.event.block.BlockRedstoneEvent; // CraftBukkit
+
 public class BlockCommandBlock extends BlockContainer
 {
     private static final String __OBFID = "CL_00000219";
@@ -31,13 +33,20 @@
             boolean flag = p_149695_1_.isBlockIndirectlyGettingPowered(p_149695_2_, p_149695_3_, p_149695_4_);
             int l = p_149695_1_.getBlockMetadata(p_149695_2_, p_149695_3_, p_149695_4_);
             boolean flag1 = (l & 1) != 0;
+            // CraftBukkit start
+            org.bukkit.block.Block bukkitBlock = p_149695_1_.getWorld().getBlockAt(p_149695_2_, p_149695_3_, p_149695_4_);
+            int old = flag1 ? 15 : 0;
+            int current = flag ? 15 : 0;
+            BlockRedstoneEvent eventRedstone = new BlockRedstoneEvent(bukkitBlock, old, current);
+            p_149695_1_.getServer().getPluginManager().callEvent(eventRedstone);
+            // CraftBukkit end
 
-            if (flag && !flag1)
+            if (eventRedstone.getNewCurrent() > 0 && !(eventRedstone.getOldCurrent() > 0))   // CraftBukkit
             {
                 p_149695_1_.setBlockMetadataWithNotify(p_149695_2_, p_149695_3_, p_149695_4_, l | 1, 4);
                 p_149695_1_.scheduleBlockUpdate(p_149695_2_, p_149695_3_, p_149695_4_, this, this.tickRate(p_149695_1_));
             }
-            else if (!flag && flag1)
+            else if (!(eventRedstone.getNewCurrent() > 0) && eventRedstone.getOldCurrent() > 0)     // CraftBukkit
             {
                 p_149695_1_.setBlockMetadataWithNotify(p_149695_2_, p_149695_3_, p_149695_4_, l & -2, 4);
             }
