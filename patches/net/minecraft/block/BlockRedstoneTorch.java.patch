--- ../src-base/minecraft/net/minecraft/block/BlockRedstoneTorch.java
+++ ../src-work/minecraft/net/minecraft/block/BlockRedstoneTorch.java
@@ -3,20 +3,25 @@
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
 import java.util.ArrayList;
-import java.util.HashMap;
+import java.util.WeakHashMap;
 import java.util.List;
 import java.util.Map;
 import java.util.Random;
+
+import io.github.crucible.CrucibleConfigs;
 import net.minecraft.creativetab.CreativeTabs;
 import net.minecraft.init.Blocks;
 import net.minecraft.item.Item;
 import net.minecraft.world.IBlockAccess;
 import net.minecraft.world.World;
 
+import org.bukkit.event.block.BlockRedstoneEvent; // CraftBukkit
+
+
 public class BlockRedstoneTorch extends BlockTorch
 {
     private boolean field_150113_a;
-    private static Map field_150112_b = new HashMap();
+    private static Map field_150112_b = new WeakHashMap();
     private static final String __OBFID = "CL_00000298";
 
     private boolean func_150111_a(World p_150111_1_, int p_150111_2_, int p_150111_3_, int p_150111_4_, boolean p_150111_5_)
@@ -115,8 +120,12 @@
         return l == 5 && p_150110_1_.getIndirectPowerOutput(p_150110_2_, p_150110_3_ - 1, p_150110_4_, 0) ? true : (l == 3 && p_150110_1_.getIndirectPowerOutput(p_150110_2_, p_150110_3_, p_150110_4_ - 1, 2) ? true : (l == 4 && p_150110_1_.getIndirectPowerOutput(p_150110_2_, p_150110_3_, p_150110_4_ + 1, 3) ? true : (l == 1 && p_150110_1_.getIndirectPowerOutput(p_150110_2_ - 1, p_150110_3_, p_150110_4_, 4) ? true : l == 2 && p_150110_1_.getIndirectPowerOutput(p_150110_2_ + 1, p_150110_3_, p_150110_4_, 5))));
     }
 
+    private long last = 0L;
+
     public void updateTick(World p_149674_1_, int p_149674_2_, int p_149674_3_, int p_149674_4_, Random p_149674_5_)
     {
+        if (System.currentTimeMillis() - last <= CrucibleConfigs.configs.cauldron_optimization_redstoneTorchUpdateSpeed) { return; }
+        last = System.currentTimeMillis();
         boolean flag = this.func_150110_m(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_);
         List list = (List)field_150112_b.get(p_149674_1_);
 
@@ -125,10 +134,30 @@
             list.remove(0);
         }
 
+        // CraftBukkit start
+        org.bukkit.plugin.PluginManager manager = p_149674_1_.getServer().getPluginManager();
+        org.bukkit.block.Block block = p_149674_1_.getWorld().getBlockAt(p_149674_2_, p_149674_3_, p_149674_4_);
+        int oldCurrent = this.field_150113_a ? 15 : 0;
+        BlockRedstoneEvent event = new BlockRedstoneEvent(block, oldCurrent, oldCurrent);
+        // CraftBukkit end
+
         if (this.field_150113_a)
         {
             if (flag)
             {
+                // CraftBukkit start
+                if (oldCurrent != 0)
+                {
+                    event.setNewCurrent(0);
+                    manager.callEvent(event);
+
+                    if (event.getNewCurrent() != 0)
+                    {
+                        return;
+                    }
+                }
+
+                // CraftBukkit end
                 p_149674_1_.setBlock(p_149674_2_, p_149674_3_, p_149674_4_, Blocks.unlit_redstone_torch, p_149674_1_.getBlockMetadata(p_149674_2_, p_149674_3_, p_149674_4_), 3);
 
                 if (this.func_150111_a(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, true))
@@ -147,6 +176,19 @@
         }
         else if (!flag && !this.func_150111_a(p_149674_1_, p_149674_2_, p_149674_3_, p_149674_4_, false))
         {
+            // CraftBukkit start
+            if (oldCurrent != 15)
+            {
+                event.setNewCurrent(15);
+                manager.callEvent(event);
+
+                if (event.getNewCurrent() != 15)
+                {
+                    return;
+                }
+            }
+
+            // CraftBukkit end
             p_149674_1_.setBlock(p_149674_2_, p_149674_3_, p_149674_4_, Blocks.redstone_torch, p_149674_1_.getBlockMetadata(p_149674_2_, p_149674_3_, p_149674_4_), 3);
         }
     }
