--- ../src-base/minecraft/net/minecraft/network/rcon/RConThreadClient.java
+++ ../src-work/minecraft/net/minecraft/network/rcon/RConThreadClient.java
@@ -41,20 +41,21 @@
 
     public void run()
     {
-        while (true)
+        try
         {
-            try
+            while (true)
             {
-                if (!this.running)
+                if (!this.running || clientSocket == null)
                 {
                     break;
                 }
 
                 BufferedInputStream bufferedinputstream = new BufferedInputStream(this.clientSocket.getInputStream());
                 int i = bufferedinputstream.read(this.buffer, 0, 1460);
-
-                if (10 > i)
+                
+                if (i < 10)
                 {
+                    this.running = false; // Cauldron
                     return;
                 }
 
@@ -110,26 +111,23 @@
                     }
                 }
             }
+            }
             catch (SocketTimeoutException sockettimeoutexception)
             {
-                break;
+                return;
             }
             catch (IOException ioexception)
             {
-                break;
+                return;
             }
             catch (Exception exception1)
             {
                 field_164005_h.error("Exception whilst parsing <PERSON>ON input", exception1);
-                break;
             }
             finally
             {
                 this.closeSocket();
             }
-
-            return;
-        }
     }
 
     private void sendResponse(int p_72654_1_, int p_72654_2_, String p_72654_3_) throws IOException
@@ -167,6 +165,7 @@
 
     private void closeSocket()
     {
+        this.running = false;
         if (null != this.clientSocket)
         {
             try
