--- ../src-base/minecraft/net/minecraft/network/rcon/RConConsoleSource.java
+++ ../src-work/minecraft/net/minecraft/network/rcon/RConConsoleSource.java
@@ -25,6 +25,13 @@
         return new ChatComponentText(this.getCommandSenderName());
     }
 
+    // CraftBukkit start - Send a String
+    public void sendMessage(String message)
+    {
+        this.buffer.append(message);
+    }
+    // CraftBukkit end
+
     public void addChatMessage(IChatComponent p_145747_1_)
     {
         this.buffer.append(p_145747_1_.getUnformattedText());
