--- ../src-base/minecraft/net/minecraft/inventory/IInventory.java
+++ ../src-work/minecraft/net/minecraft/inventory/IInventory.java
@@ -3,6 +3,8 @@
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.item.ItemStack;
 
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity; // CraftBukkit
+
 public interface IInventory
 {
     int getSizeInventory();
@@ -30,4 +32,20 @@
     void closeInventory();
 
     boolean isItemValidForSlot(int p_94041_1_, ItemStack p_94041_2_);
+
+    // CraftBukkit start
+    ItemStack[] getContents();
+
+    void onOpen(CraftHumanEntity who);
+
+    void onClose(CraftHumanEntity who);
+
+    java.util.List<org.bukkit.entity.HumanEntity> getViewers();
+
+    org.bukkit.inventory.InventoryHolder getOwner();
+
+    void setMaxStackSize(int size);
+
+    int MAX_STACK = 64;
+    // CraftBukkit end
 }
