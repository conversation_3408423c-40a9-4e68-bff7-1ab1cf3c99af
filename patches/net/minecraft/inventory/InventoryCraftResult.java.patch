--- ../src-base/minecraft/net/minecraft/inventory/InventoryCraftResult.java
+++ ../src-work/minecraft/net/minecraft/inventory/InventoryCraftResult.java
@@ -2,10 +2,41 @@
 
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.item.ItemStack;
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity;
+import org.bukkit.entity.HumanEntity;
+// CraftBukkit end
 
 public class InventoryCraftResult implements IInventory
 {
     private ItemStack[] stackResult = new ItemStack[1];
+
+    // CraftBukkit start
+    private int maxStack = MAX_STACK;
+
+    public ItemStack[] getContents()
+    {
+        return this.stackResult;
+    }
+
+    public org.bukkit.inventory.InventoryHolder getOwner()
+    {
+        return null; // Result slots don't get an owner
+    }
+
+    // Don't need a transaction; the InventoryCrafting keeps track of it for us
+    public void onOpen(CraftHumanEntity who) {}
+    public void onClose(CraftHumanEntity who) {}
+    public java.util.List<HumanEntity> getViewers()
+    {
+        return new java.util.ArrayList<HumanEntity>();
+    }
+
+    public void setMaxStackSize(int size)
+    {
+        maxStack = size;
+    }
+    // CraftBukkit end
     private static final String __OBFID = "CL_00001760";
 
     public int getSizeInventory()
@@ -63,7 +94,7 @@
 
     public int getInventoryStackLimit()
     {
-        return 64;
+        return maxStack; // CraftBukkit
     }
 
     public void markDirty() {}
