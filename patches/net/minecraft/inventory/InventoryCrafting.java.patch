--- ../src-base/minecraft/net/minecraft/inventory/InventoryCrafting.java
+++ ../src-work/minecraft/net/minecraft/inventory/InventoryCrafting.java
@@ -3,11 +3,69 @@
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.item.ItemStack;
 
+// CraftBukkit start
+import java.util.List;
+import net.minecraft.item.crafting.IRecipe;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity;
+import org.bukkit.entity.HumanEntity;
+import org.bukkit.event.inventory.InventoryType;
+// CraftBukkit end
+
 public class InventoryCrafting implements IInventory
 {
     private ItemStack[] stackList;
     private int inventoryWidth;
     private Container eventHandler;
+    // CraftBukkit start
+    public List<HumanEntity> transaction = new java.util.ArrayList<HumanEntity>();
+    public IRecipe currentRecipe;
+    public IInventory resultInventory;
+    private EntityPlayer owner;
+    private int maxStack = MAX_STACK;
+
+    public ItemStack[] getContents()
+    {
+        return this.stackList;
+    }
+
+    public void onOpen(CraftHumanEntity who)
+    {
+        transaction.add(who);
+    }
+
+    public InventoryType getInvType()
+    {
+        return stackList.length == 4 ? InventoryType.CRAFTING : InventoryType.WORKBENCH;
+    }
+
+    public void onClose(CraftHumanEntity who)
+    {
+        transaction.remove(who);
+    }
+
+    public List<HumanEntity> getViewers()
+    {
+        return transaction;
+    }
+
+    public org.bukkit.inventory.InventoryHolder getOwner()
+    {
+        if (owner == null) return null; // Cauldron
+        return owner.getBukkitEntity();
+    }
+
+    public void setMaxStackSize(int size)
+    {
+        maxStack = size;
+        resultInventory.setMaxStackSize(size);
+    }
+
+    public InventoryCrafting(Container container, int i, int j, EntityPlayer player)
+    {
+        this(container, i, j);
+        this.owner = player;
+    }
+    // CraftBukkit end
     private static final String __OBFID = "CL_00001743";
 
     public InventoryCrafting(Container p_i1807_1_, int p_i1807_2_, int p_i1807_3_)
@@ -30,15 +88,7 @@
 
     public ItemStack getStackInRowAndColumn(int p_70463_1_, int p_70463_2_)
     {
-        if (p_70463_1_ >= 0 && p_70463_1_ < this.inventoryWidth)
-        {
-            int k = p_70463_1_ + p_70463_2_ * this.inventoryWidth;
-            return this.getStackInSlot(k);
-        }
-        else
-        {
-            return null;
-        }
+    	return (p_70463_1_ >= 0 && p_70463_1_ < this.inventoryWidth) ?  this.getStackInSlot(p_70463_1_ + p_70463_2_ * this.inventoryWidth) : null;
     }
 
     public String getInventoryName()
@@ -105,7 +155,7 @@
 
     public int getInventoryStackLimit()
     {
-        return 64;
+        return maxStack; // CraftBukkit
     }
 
     public void markDirty() {}
