--- ../src-base/minecraft/net/minecraft/inventory/ContainerRepair.java
+++ ../src-work/minecraft/net/minecraft/inventory/ContainerRepair.java
@@ -17,19 +17,15 @@
 import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
 
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftInventoryView; // CraftBukkit
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftInventory;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftInventoryAnvil;
+
 public class ContainerRepair extends Container
 {
     private static final Logger logger = LogManager.getLogger();
     private IInventory outputSlot = new InventoryCraftResult();
-    private IInventory inputSlots = new InventoryBasic("Repair", true, 2)
-    {
-        private static final String __OBFID = "CL_00001733";
-        public void markDirty()
-        {
-            super.markDirty();
-            ContainerRepair.this.onCraftMatrixChanged(this);
-        }
-    };
+    private IInventory inputSlots = new ContainerRepairInventory(this, "Repair", true, 2);
     private World theWorld;
     private int field_82861_i;
     private int field_82858_j;
@@ -39,9 +35,14 @@
     private String repairedItemName;
     private final EntityPlayer thePlayer;
     private static final String __OBFID = "CL_00001732";
+    // CraftBukkit start
+    private CraftInventoryView bukkitEntity = null;
+    private InventoryPlayer player;
 
     public ContainerRepair(InventoryPlayer p_i1800_1_, final World p_i1800_2_, final int p_i1800_3_, final int p_i1800_4_, final int p_i1800_5_, EntityPlayer p_i1800_6_)
     {
+        this.player = p_i1800_1_;
+        // CraftBukkit end
         this.theWorld = p_i1800_2_;
         this.field_82861_i = p_i1800_3_;
         this.field_82858_j = p_i1800_4_;
@@ -461,6 +462,11 @@
 
     public boolean canInteractWith(EntityPlayer p_75145_1_)
     {
+        if (!this.checkReachable)
+        {
+            return true; // CraftBukkit
+        }
+
         return this.theWorld.getBlock(this.field_82861_i, this.field_82858_j, this.field_82859_k) != Blocks.anvil ? false : p_75145_1_.getDistanceSq((double)this.field_82861_i + 0.5D, (double)this.field_82858_j + 0.5D, (double)this.field_82859_k + 0.5D) <= 64.0D;
     }
 
@@ -535,4 +541,18 @@
 
         this.updateRepairOutput();
     }
+
+    // CraftBukkit start
+    public CraftInventoryView getBukkitView()
+    {
+        if (bukkitEntity != null)
+        {
+            return bukkitEntity;
+        }
+
+        CraftInventory inventory = new CraftInventoryAnvil(this.inputSlots, this.outputSlot);
+        bukkitEntity = new CraftInventoryView(this.player.player.getBukkitEntity(), inventory, this);
+        return bukkitEntity;
+    }
+    // CraftBukkit end
 }
