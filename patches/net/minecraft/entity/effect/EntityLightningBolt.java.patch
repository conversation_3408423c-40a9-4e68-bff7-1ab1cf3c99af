--- ../src-base/minecraft/net/minecraft/entity/effect/EntityLightningBolt.java
+++ ../src-work/minecraft/net/minecraft/entity/effect/EntityLightningBolt.java
@@ -10,6 +10,8 @@
 import net.minecraft.world.EnumDifficulty;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory; // CraftBukkit
+
 public class EntityLightningBolt extends EntityWeatherEffect
 {
     private int lightningState;
@@ -17,15 +19,25 @@
     private int boltLivingTime;
     private static final String __OBFID = "CL_00001666";
 
+    // CraftBukkit start
+    public boolean isEffect = false;
+
     public EntityLightningBolt(World p_i1703_1_, double p_i1703_2_, double p_i1703_4_, double p_i1703_6_)
     {
+        this(p_i1703_1_, p_i1703_2_, p_i1703_4_, p_i1703_6_, false);
+    }
+
+    public EntityLightningBolt(World p_i1703_1_, double p_i1703_2_, double p_i1703_4_, double p_i1703_6_, boolean isEffect)
+    {
         super(p_i1703_1_);
+        this.isEffect = isEffect;
+        // CraftBukkit end
         this.setLocationAndAngles(p_i1703_2_, p_i1703_4_, p_i1703_6_, 0.0F, 0.0F);
         this.lightningState = 2;
         this.boltVertex = this.rand.nextLong();
         this.boltLivingTime = this.rand.nextInt(3) + 1;
 
-        if (!p_i1703_1_.isRemote && p_i1703_1_.getGameRules().getGameRuleBooleanValue("doFireTick") && (p_i1703_1_.difficultySetting == EnumDifficulty.NORMAL || p_i1703_1_.difficultySetting == EnumDifficulty.HARD) && p_i1703_1_.doChunksNearChunkExist(MathHelper.floor_double(p_i1703_2_), MathHelper.floor_double(p_i1703_4_), MathHelper.floor_double(p_i1703_6_), 10))
+        if (!isEffect && !p_i1703_1_.isRemote && p_i1703_1_.getGameRules().getGameRuleBooleanValue("doFireTick") && (p_i1703_1_.difficultySetting == EnumDifficulty.NORMAL || p_i1703_1_.difficultySetting == EnumDifficulty.HARD) && p_i1703_1_.doChunksNearChunkExist(MathHelper.floor_double(p_i1703_2_), MathHelper.floor_double(p_i1703_4_), MathHelper.floor_double(p_i1703_6_), 10))  // CraftBukkit
         {
             int i = MathHelper.floor_double(p_i1703_2_);
             int j = MathHelper.floor_double(p_i1703_4_);
@@ -33,7 +45,13 @@
 
             if (p_i1703_1_.getBlock(i, j, k).getMaterial() == Material.air && Blocks.fire.canPlaceBlockAt(p_i1703_1_, i, j, k))
             {
-                p_i1703_1_.setBlock(i, j, k, Blocks.fire);
+                // CraftBukkit start
+                if (!CraftEventFactory.callBlockIgniteEvent(p_i1703_1_, i, j, k, this).isCancelled())
+                {
+                    p_i1703_1_.setBlock(i, j, k, Blocks.fire);
+                }
+
+                // CraftBukkit end
             }
 
             for (i = 0; i < 4; ++i)
@@ -44,7 +62,13 @@
 
                 if (p_i1703_1_.getBlock(j, k, l).getMaterial() == Material.air && Blocks.fire.canPlaceBlockAt(p_i1703_1_, j, k, l))
                 {
-                    p_i1703_1_.setBlock(j, k, l, Blocks.fire);
+                    // CraftBukkit start
+                    if (!CraftEventFactory.callBlockIgniteEvent(p_i1703_1_, j, k, l, this).isCancelled())
+                    {
+                        p_i1703_1_.setBlock(j, k, l, Blocks.fire);
+                    }
+
+                    // CraftBukkit end
                 }
             }
         }
@@ -74,7 +98,8 @@
                 this.lightningState = 1;
                 this.boltVertex = this.rand.nextLong();
 
-                if (!this.worldObj.isRemote && this.worldObj.getGameRules().getGameRuleBooleanValue("doFireTick") && this.worldObj.doChunksNearChunkExist(MathHelper.floor_double(this.posX), MathHelper.floor_double(this.posY), MathHelper.floor_double(this.posZ), 10))
+                // CraftBukkit
+                if (!isEffect && !this.worldObj.isRemote && this.worldObj.getGameRules().getGameRuleBooleanValue("doFireTick") && this.worldObj.doChunksNearChunkExist(MathHelper.floor_double(this.posX), MathHelper.floor_double(this.posY), MathHelper.floor_double(this.posZ), 10))
                 {
                     int i = MathHelper.floor_double(this.posX);
                     int j = MathHelper.floor_double(this.posY);
@@ -82,13 +107,19 @@
 
                     if (this.worldObj.getBlock(i, j, k).getMaterial() == Material.air && Blocks.fire.canPlaceBlockAt(this.worldObj, i, j, k))
                     {
-                        this.worldObj.setBlock(i, j, k, Blocks.fire);
+                        // CraftBukkit start
+                        if (!CraftEventFactory.callBlockIgniteEvent(worldObj, i, j, k, this).isCancelled())
+                        {
+                            this.worldObj.setBlock(i, j, k, Blocks.fire);
+                        }
+
+                        // CraftBukkit end
                     }
                 }
             }
         }
 
-        if (this.lightningState >= 0)
+        if (this.lightningState >= 0 && !this.isEffect)   // CraftBukkit - add !this.isEffect
         {
             if (this.worldObj.isRemote)
             {
