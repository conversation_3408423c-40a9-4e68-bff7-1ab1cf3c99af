--- ../src-base/minecraft/net/minecraft/entity/item/EntityFallingBlock.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityFallingBlock.java
@@ -2,8 +2,10 @@
 
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
+
 import java.util.ArrayList;
 import java.util.Iterator;
+
 import net.minecraft.block.Block;
 import net.minecraft.block.BlockFalling;
 import net.minecraft.block.ITileEntityProvider;
@@ -19,9 +21,11 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory; // CraftBukkit
+
 public class EntityFallingBlock extends Entity
 {
-    private Block field_145811_e;
+    public Block field_145811_e; // CraftBukkit - private -> public
     public int field_145814_a;
     public int field_145812_b;
     public boolean field_145813_c;
@@ -103,7 +107,8 @@
 
                 if (this.field_145812_b == 1)
                 {
-                    if (this.worldObj.getBlock(i, j, k) != this.field_145811_e)
+                    // CraftBukkit - compare data and call event
+                    if (this.field_145812_b != 1 || this.worldObj.getBlock(i, j, k) != this.field_145811_e || this.worldObj.getBlockMetadata(i, j, k) != this.field_145814_a || CraftEventFactory.callEntityChangeBlockEvent(this, i, j, k, Blocks.air, 0).isCancelled())
                     {
                         this.setDead();
                         return;
@@ -122,8 +127,17 @@
                     {
                         this.setDead();
 
-                        if (!this.field_145808_f && this.worldObj.canPlaceEntityOnSide(this.field_145811_e, i, j, k, true, 1, (Entity)null, (ItemStack)null) && !BlockFalling.func_149831_e(this.worldObj, i, j - 1, k) && this.worldObj.setBlock(i, j, k, this.field_145811_e, this.field_145814_a, 3))
+                        // CraftBukkit start
+                        if (!this.field_145808_f && this.worldObj.canPlaceEntityOnSide(this.field_145811_e, i, j, k, true, 1, (Entity) null, (ItemStack) null) && !BlockFalling.func_149831_e(this.worldObj, i, j - 1, k) /* mimic the false conditions of setTypeIdAndData */ && i >= -30000000 && k >= -30000000 && i < 30000000 && k < 30000000 && j > 0 && j < 256 && !(this.worldObj.getBlock(i, j, k) == this.field_145811_e && this.worldObj.getBlockMetadata(i, j, k) == this.field_145814_a))
                         {
+                            if (CraftEventFactory.callEntityChangeBlockEvent(this, i, j, k, this.field_145811_e, this.field_145814_a).isCancelled())
+                            {
+                                return;
+                            }
+
+                            this.worldObj.setBlock(i, j, k, this.field_145811_e, this.field_145814_a, 3);
+                            // CraftBukkit end
+
                             if (this.field_145811_e instanceof BlockFalling)
                             {
                                 ((BlockFalling)this.field_145811_e).func_149828_a(this.worldObj, i, j, k, this.field_145814_a);
@@ -189,8 +203,10 @@
 
                 while (iterator.hasNext())
                 {
-                    Entity entity = (Entity)iterator.next();
-                    entity.attackEntityFrom(damagesource, (float)Math.min(MathHelper.floor_float((float)i * this.field_145816_i), this.field_145815_h));
+                    Entity entity = (Entity) iterator.next();
+                    CraftEventFactory.entityDamage = this; // CraftBukkit
+                    entity.attackEntityFrom(damagesource,(float) Math.min(MathHelper.floor_float((float) i * this.field_145816_i), this.field_145815_h));
+                    CraftEventFactory.entityDamage = null; // CraftBukkit
                 }
 
                 if (flag && (double)this.rand.nextFloat() < 0.05000000074505806D + (double)i * 0.05D)
@@ -304,4 +320,11 @@
     {
         return this.field_145811_e;
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
