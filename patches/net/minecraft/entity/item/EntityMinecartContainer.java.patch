--- ../src-base/minecraft/net/minecraft/entity/item/EntityMinecartContainer.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityMinecartContainer.java
@@ -9,12 +9,61 @@
 import net.minecraft.util.DamageSource;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import java.util.List;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftHumanEntity;
+import org.bukkit.entity.HumanEntity;
+import org.bukkit.inventory.InventoryHolder;
+// CraftBukkit end
+
 public abstract class EntityMinecartContainer extends EntityMinecart implements IInventory
 {
     private ItemStack[] minecartContainerItems = new ItemStack[36];
     private boolean dropContentsWhenDead = true;
     private static final String __OBFID = "CL_00001674";
 
+    // CraftBukkit start
+    public List<HumanEntity> transaction = new java.util.ArrayList<HumanEntity>();
+    private int maxStack = MAX_STACK;
+
+    public ItemStack[] getContents()
+    {
+        return this.minecartContainerItems;
+    }
+
+    public void onOpen(CraftHumanEntity who)
+    {
+        transaction.add(who);
+    }
+
+    public void onClose(CraftHumanEntity who)
+    {
+        transaction.remove(who);
+    }
+
+    public List<HumanEntity> getViewers()
+    {
+        return transaction;
+    }
+
+    public InventoryHolder getOwner()
+    {
+        org.bukkit.entity.Entity cart = getBukkitEntity();
+
+        if (cart instanceof InventoryHolder)
+        {
+            return (InventoryHolder) cart;
+        }
+
+        return null;
+    }
+
+    public void setMaxStackSize(int size)
+    {
+        maxStack = size;
+    }
+    // CraftBukkit end
+
     public EntityMinecartContainer(World p_i1716_1_)
     {
         super(p_i1716_1_);
@@ -147,8 +196,16 @@
 
     public void travelToDimension(int p_71027_1_)
     {
+        // Spigot Start
+        for (HumanEntity human : new java.util.ArrayList<HumanEntity>(transaction))
+        {
+            human.closeInventory();
+        }
+
+        // Spigot End
         this.dropContentsWhenDead = false;
         super.travelToDimension(p_71027_1_);
+        this.minecartContainerItems = new ItemStack[36]; // Drop the inventory to fix dupe glitch
     }
 
     public void setDead()
