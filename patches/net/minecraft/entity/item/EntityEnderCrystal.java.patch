--- ../src-base/minecraft/net/minecraft/entity/item/EntityEnderCrystal.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityEnderCrystal.java
@@ -10,6 +10,8 @@
 import net.minecraft.world.World;
 import net.minecraft.world.WorldProviderEnd;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory; // CraftBukkit
+
 public class EntityEnderCrystal extends Entity
 {
     public int innerRotation;
@@ -56,7 +58,13 @@
 
         if (this.worldObj.provider instanceof WorldProviderEnd && this.worldObj.getBlock(i, j, k) != Blocks.fire)
         {
-            this.worldObj.setBlock(i, j, k, Blocks.fire);
+            // CraftBukkit start
+            if (!CraftEventFactory.callBlockIgniteEvent(this.worldObj, i, j, k, this).isCancelled())
+            {
+                this.worldObj.setBlock(i, j, k, Blocks.fire);
+            }
+
+            // CraftBukkit end
         }
     }
 
@@ -85,6 +93,13 @@
         {
             if (!this.isDead && !this.worldObj.isRemote)
             {
+                // CraftBukkit start - All non-living entities need this
+                if (CraftEventFactory.handleNonLivingEntityDamageEvent(this, p_70097_1_, p_70097_2_))
+                {
+                    return false;
+                }
+
+                // CraftBukkit end
                 this.health = 0;
 
                 if (this.health <= 0)
@@ -93,7 +108,7 @@
 
                     if (!this.worldObj.isRemote)
                     {
-                        this.worldObj.createExplosion((Entity)null, this.posX, this.posY, this.posZ, 6.0F, true);
+                        this.worldObj.createExplosion(this, this.posX, this.posY, this.posZ, 6.0F, true); // CraftBukkit - (Entity) null -> this
                     }
                 }
             }
@@ -101,4 +116,11 @@
             return true;
         }
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
