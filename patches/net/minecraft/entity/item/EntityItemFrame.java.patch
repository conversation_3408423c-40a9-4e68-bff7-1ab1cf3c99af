--- ../src-base/minecraft/net/minecraft/entity/item/EntityItemFrame.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityItemFrame.java
@@ -12,6 +12,7 @@
 import net.minecraft.util.DamageSource;
 import net.minecraft.world.World;
 import net.minecraft.world.storage.MapData;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class EntityItemFrame extends EntityHanging
 {
@@ -45,6 +46,13 @@
         {
             if (!this.worldObj.isRemote)
             {
+                // CraftBukkit start
+                if (CraftEventFactory.handleNonLivingEntityDamageEvent(this, p_70097_1_, p_70097_2_) || this.isDead)
+                {
+                    return true;
+                }
+                // CraftBukkit end
+
                 this.func_146065_b(p_70097_1_.getEntity(), false);
                 this.setDisplayedItem((ItemStack)null);
             }
@@ -115,7 +123,7 @@
             if (p_110131_1_.getItem() == Items.filled_map)
             {
                 MapData mapdata = ((ItemMap)p_110131_1_.getItem()).getMapData(p_110131_1_, this.worldObj);
-                mapdata.playersVisibleOnMap.remove("frame-" + this.getEntityId());
+                mapdata.playersVisibleOnMap.remove(java.util.UUID.nameUUIDFromBytes(("frame-" + this.getEntityId()).getBytes(org.apache.commons.codec.Charsets.US_ASCII))); // Spigot
             }
 
             p_110131_1_.setItemFrame((EntityItemFrame)null);
@@ -203,4 +211,11 @@
 
         return true;
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
