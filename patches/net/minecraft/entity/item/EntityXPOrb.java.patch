--- ../src-base/minecraft/net/minecraft/entity/item/EntityXPOrb.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityXPOrb.java
@@ -12,13 +12,20 @@
 import net.minecraftforge.common.MinecraftForge;
 import net.minecraftforge.event.entity.player.PlayerPickupXpEvent;
 
+
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.event.entity.EntityTargetEvent;
+// CraftBukkit end
+
 public class EntityXPOrb extends Entity
 {
     public int xpColor;
     public int xpOrbAge;
     public int field_70532_c;
     private int xpOrbHealth = 5;
-    public int xpValue;
+    public int xpValue; // CraftBukkit - private -> public
     private EntityPlayer closestPlayer;
     private int xpTargetColor;
     private static final String __OBFID = "CL_00001544";
@@ -115,18 +122,27 @@
 
         if (this.closestPlayer != null)
         {
-            double d1 = (this.closestPlayer.posX - this.posX) / d0;
-            double d2 = (this.closestPlayer.posY + (double)this.closestPlayer.getEyeHeight() - this.posY) / d0;
-            double d3 = (this.closestPlayer.posZ - this.posZ) / d0;
-            double d4 = Math.sqrt(d1 * d1 + d2 * d2 + d3 * d3);
-            double d5 = 1.0D - d4;
+            // CraftBukkit start
+            EntityTargetEvent event = CraftEventFactory.callEntityTargetEvent(this, closestPlayer, EntityTargetEvent.TargetReason.CLOSEST_PLAYER);
+            Entity target = event.getTarget() == null ? null : ((CraftEntity) event.getTarget()).getHandle();
 
-            if (d5 > 0.0D)
+            if (!event.isCancelled() && target != null)
             {
-                d5 *= d5;
-                this.motionX += d1 / d4 * d5 * 0.1D;
-                this.motionY += d2 / d4 * d5 * 0.1D;
-                this.motionZ += d3 / d4 * d5 * 0.1D;
+                double d1 = (target.posX - this.posX) / d0;
+                double d2 = (target.posY + (double) target.getEyeHeight() - this.posY) / d0;
+                double d3 = (target.posZ - this.posZ) / d0;
+                double d4 = Math.sqrt(d1 * d1 + d2 * d2 + d3 * d3);
+                double d5 = 1.0D - d4;
+
+                if (d5 > 0.0D)
+                {
+                    d5 *= d5;
+                    this.motionX += d1 / d4 * d5 * 0.1D;
+                    this.motionY += d2 / d4 * d5 * 0.1D;
+                    this.motionZ += d3 / d4 * d5 * 0.1D;
+                }
+
+                // CraftBukkit end
             }
         }
 
@@ -210,7 +226,7 @@
                 p_70100_1_.xpCooldown = 2;
                 this.worldObj.playSoundAtEntity(p_70100_1_, "random.orb", 0.1F, 0.5F * ((this.rand.nextFloat() - this.rand.nextFloat()) * 0.7F + 1.8F));
                 p_70100_1_.onItemPickup(this, 1);
-                p_70100_1_.addExperience(this.xpValue);
+                p_70100_1_.addExperience(CraftEventFactory.callPlayerExpChangeEvent(p_70100_1_, this.xpValue).getAmount());
                 this.setDead();
             }
         }
@@ -229,6 +245,88 @@
 
     public static int getXPSplit(int p_70527_0_)
     {
+        // CraftBukkit start
+        if (p_70527_0_ > 162670129)
+        {
+            return p_70527_0_ - 100000;
+        }
+
+        if (p_70527_0_ > 81335063)
+        {
+            return 81335063;
+        }
+
+        if (p_70527_0_ > 40667527)
+        {
+            return 40667527;
+        }
+
+        if (p_70527_0_ > 20333759)
+        {
+            return 20333759;
+        }
+
+        if (p_70527_0_ > 10166857)
+        {
+            return 10166857;
+        }
+
+        if (p_70527_0_ > 5083423)
+        {
+            return 5083423;
+        }
+
+        if (p_70527_0_ > 2541701)
+        {
+            return 2541701;
+        }
+
+        if (p_70527_0_ > 1270849)
+        {
+            return 1270849;
+        }
+
+        if (p_70527_0_ > 635413)
+        {
+            return 635413;
+        }
+
+        if (p_70527_0_ > 317701)
+        {
+            return 317701;
+        }
+
+        if (p_70527_0_ > 158849)
+        {
+            return 158849;
+        }
+
+        if (p_70527_0_ > 79423)
+        {
+            return 79423;
+        }
+
+        if (p_70527_0_ > 39709)
+        {
+            return 39709;
+        }
+
+        if (p_70527_0_ > 19853)
+        {
+            return 19853;
+        }
+
+        if (p_70527_0_ > 9923)
+        {
+            return 9923;
+        }
+
+        if (p_70527_0_ > 4957)
+        {
+            return 4957;
+        }
+
+        // CraftBukkit end
         return p_70527_0_ >= 2477 ? 2477 : (p_70527_0_ >= 1237 ? 1237 : (p_70527_0_ >= 617 ? 617 : (p_70527_0_ >= 307 ? 307 : (p_70527_0_ >= 149 ? 149 : (p_70527_0_ >= 73 ? 73 : (p_70527_0_ >= 37 ? 37 : (p_70527_0_ >= 17 ? 17 : (p_70527_0_ >= 7 ? 7 : (p_70527_0_ >= 3 ? 3 : 1)))))))));
     }
 
@@ -236,4 +334,11 @@
     {
         return false;
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
