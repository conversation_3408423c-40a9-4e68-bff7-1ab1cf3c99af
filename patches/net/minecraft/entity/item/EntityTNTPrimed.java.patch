--- ../src-base/minecraft/net/minecraft/entity/item/EntityTNTPrimed.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityTNTPrimed.java
@@ -5,13 +5,24 @@
 import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLivingBase;
 import net.minecraft.nbt.NBTTagCompound;
+import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
+import org.bukkit.craftbukkit.v1_7_R4.CraftServer;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import thermos.thermite.ThermiteRandom;
 
+import java.util.Random;
+
+import org.bukkit.event.entity.ExplosionPrimeEvent; // CraftBukkit
+
 public class EntityTNTPrimed extends Entity
 {
     public int fuse;
     private EntityLivingBase tntPlacedBy;
     private static final String __OBFID = "CL_00001681";
+    public float yield = 4; // CraftBukkit
+    public boolean isIncendiary = false; // CraftBukkit
+    private static final Random random = new ThermiteRandom();
 
     public EntityTNTPrimed(World p_i1729_1_)
     {
@@ -25,10 +36,10 @@
     {
         this(p_i1730_1_);
         this.setPosition(p_i1730_2_, p_i1730_4_, p_i1730_6_);
-        float f = (float)(Math.random() * Math.PI * 2.0D);
-        this.motionX = (double)(-((float)Math.sin((double)f)) * 0.02F);
+        float f = (float)(random.nextDouble() * Math.PI * 2.0D);
+        this.motionX = (double)(-(MathHelper.sin(f)) * 0.02F);
         this.motionY = 0.20000000298023224D;
-        this.motionZ = (double)(-((float)Math.cos((double)f)) * 0.02F);
+        this.motionZ = (double)(-(MathHelper.cos(f)) * 0.02F);
         this.fuse = 80;
         this.prevPosX = p_i1730_2_;
         this.prevPosY = p_i1730_4_;
@@ -50,6 +61,7 @@
 
     public void onUpdate()
     {
+        if (worldObj.spigotConfig.currentPrimedTnt++ > worldObj.spigotConfig.maxTntTicksPerTick) { return; } // Spigot
         this.prevPosX = this.posX;
         this.prevPosY = this.posY;
         this.prevPosZ = this.posZ;
@@ -68,12 +80,14 @@
 
         if (this.fuse-- <= 0)
         {
-            this.setDead();
-
+            // CraftBukkit start - Need to reverse the order of the explosion and the entity death so we have a location for the event
             if (!this.worldObj.isRemote)
             {
                 this.explode();
             }
+
+            this.setDead();
+            // CraftBukkit end
         }
         else
         {
@@ -83,8 +97,19 @@
 
     private void explode()
     {
-        float f = 4.0F;
-        this.worldObj.createExplosion(this, this.posX, this.posY, this.posZ, f, true);
+        // CraftBukkit start
+        // float f = 4.0F;
+        CraftServer server = this.worldObj.getServer();
+        ExplosionPrimeEvent event = new ExplosionPrimeEvent((org.bukkit.entity.Explosive) CraftEntity.getEntity(server, this));
+        server.getPluginManager().callEvent(event);
+
+        if (!event.isCancelled())
+        {
+            // give 'this' instead of (Entity) null so we know what causes the damage
+            this.worldObj.newExplosion(this, this.posX, this.posY, this.posZ, event.getRadius(), event.getFire(), true);
+        }
+
+        // CraftBukkit end
     }
 
     protected void writeEntityToNBT(NBTTagCompound p_70014_1_)
@@ -107,4 +132,11 @@
     {
         return this.tntPlacedBy;
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
