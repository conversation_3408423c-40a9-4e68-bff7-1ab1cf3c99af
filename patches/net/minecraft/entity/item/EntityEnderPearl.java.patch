--- ../src-base/minecraft/net/minecraft/entity/item/EntityEnderPearl.java
+++ ../src-work/minecraft/net/minecraft/entity/item/EntityEnderPearl.java
@@ -2,7 +2,6 @@
 
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
-import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLivingBase;
 import net.minecraft.entity.player.EntityPlayerMP;
 import net.minecraft.entity.projectile.EntityThrowable;
@@ -12,6 +11,14 @@
 import net.minecraftforge.common.MinecraftForge;
 import net.minecraftforge.event.entity.living.EnderTeleportEvent;
 
+
+// CraftBukkit start
+import org.bukkit.Bukkit;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftPlayer;
+import org.bukkit.event.player.PlayerTeleportEvent;
+// CraftBukkit end
+
 public class EntityEnderPearl extends EntityThrowable
 {
     private static final String __OBFID = "CL_00001725";
@@ -52,22 +59,42 @@
 
                 if (entityplayermp.playerNetServerHandler.func_147362_b().isChannelOpen() && entityplayermp.worldObj == this.worldObj)
                 {
-                    EnderTeleportEvent event = new EnderTeleportEvent(entityplayermp, this.posX, this.posY, this.posZ, 5.0F);
-                    if (!MinecraftForge.EVENT_BUS.post(event))
-                    { // Don't indent to lower patch size
-                    if (this.getThrower().isRiding())
-                    {
-                        this.getThrower().mountEntity((Entity)null);
+                    EnderTeleportEvent event = new EnderTeleportEvent(entityplayermp, this.posX, this.posY, this.posZ, 5);
+                    // Cauldron start - invert condition; return if cancelled otherwise fall through to CB event
+                    if (MinecraftForge.EVENT_BUS.post(event)){
+                        this.setDead();
+                        return;
                     }
+                    // Cauldron end
+                    // CraftBukkit start
+                    CraftPlayer player = entityplayermp.getBukkitEntity();
+                    org.bukkit.Location location = getBukkitEntity().getLocation();
+                    location.setPitch(player.getLocation().getPitch());
+                    location.setYaw(player.getLocation().getYaw());
+                    PlayerTeleportEvent teleEvent = new PlayerTeleportEvent(player, player.getLocation(), location, PlayerTeleportEvent.TeleportCause.ENDER_PEARL);
+                    Bukkit.getPluginManager().callEvent(teleEvent);
 
-                    this.getThrower().setPositionAndUpdate(event.targetX, event.targetY, event.targetZ);
-                    this.getThrower().fallDistance = 0.0F;
-                    this.getThrower().attackEntityFrom(DamageSource.fall, event.attackDamage);
+                    if (!teleEvent.isCancelled() && !entityplayermp.playerNetServerHandler.isDisconnected())
+                    {
+                        entityplayermp.playerNetServerHandler.teleport(teleEvent.getTo());
+                        this.getThrower().fallDistance = 0.0F;
+                        CraftEventFactory.entityDamage = this;
+                        this.getThrower().attackEntityFrom(DamageSource.fall, 5.0F);
+                        CraftEventFactory.entityDamage = null;
                     }
+
+                    // CraftBukkit end
                 }
             }
 
             this.setDead();
         }
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
