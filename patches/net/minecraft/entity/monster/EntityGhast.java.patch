--- ../src-base/minecraft/net/minecraft/entity/monster/EntityGhast.java
+++ ../src-work/minecraft/net/minecraft/entity/monster/EntityGhast.java
@@ -18,6 +18,11 @@
 import net.minecraft.world.EnumDifficulty;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.event.entity.EntityTargetEvent;
+// CraftBukkit end
+
 public class EntityGhast extends EntityFlying implements IMob
 {
     public int courseChangeCooldown;
@@ -117,13 +122,50 @@
 
         if (this.targetedEntity != null && this.targetedEntity.isDead)
         {
-            this.targetedEntity = null;
+            // CraftBukkit start
+            EntityTargetEvent event = new EntityTargetEvent(this.getBukkitEntity(), null, EntityTargetEvent.TargetReason.TARGET_DIED);
+            this.worldObj.getServer().getPluginManager().callEvent(event);
+
+            if (!event.isCancelled())
+            {
+                if (event.getTarget() == null)
+                {
+                    this.targetedEntity = null;
+                }
+                else
+                {
+                    this.targetedEntity = ((CraftEntity) event.getTarget()).getHandle();
+                }
+            }
+
+            // CraftBukkit end
         }
 
         if (this.targetedEntity == null || this.aggroCooldown-- <= 0)
         {
-            this.targetedEntity = this.worldObj.getClosestVulnerablePlayerToEntity(this, 100.0D);
+            // CraftBukkit start
+            Entity target = this.worldObj.getClosestVulnerablePlayerToEntity(this, 100.0D);
 
+            if (target != null)
+            {
+                EntityTargetEvent event = new EntityTargetEvent(this.getBukkitEntity(), target.getBukkitEntity(), EntityTargetEvent.TargetReason.CLOSEST_PLAYER);
+                this.worldObj.getServer().getPluginManager().callEvent(event);
+
+                if (!event.isCancelled())
+                {
+                    if (event.getTarget() == null)
+                    {
+                        this.targetedEntity = null;
+                    }
+                    else
+                    {
+                        this.targetedEntity = ((CraftEntity) event.getTarget()).getHandle();
+                    }
+                }
+            }
+
+            // CraftBukkit end
+
             if (this.targetedEntity != null)
             {
                 this.aggroCooldown = 20;
@@ -152,7 +194,8 @@
                 {
                     this.worldObj.playAuxSFXAtEntity((EntityPlayer)null, 1008, (int)this.posX, (int)this.posY, (int)this.posZ, 0);
                     EntityLargeFireball entitylargefireball = new EntityLargeFireball(this.worldObj, this, d5, d6, d7);
-                    entitylargefireball.field_92057_e = this.explosionStrength;
+                    // CraftBukkit - set bukkitYield when setting explosionpower
+                    entitylargefireball.bukkitYield = entitylargefireball.field_92057_e  = this.explosionStrength;
                     double d8 = 4.0D;
                     Vec3 vec3 = this.getLook(1.0F);
                     entitylargefireball.posX = this.posX + vec3.xCoord * d8;
