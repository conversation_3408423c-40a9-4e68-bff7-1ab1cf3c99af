--- ../src-base/minecraft/net/minecraft/entity/monster/EntitySnowman.java
+++ ../src-work/minecraft/net/minecraft/entity/monster/EntitySnowman.java
@@ -19,6 +19,12 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.util.CraftMagicNumbers;
+import org.bukkit.event.block.EntityBlockFormEvent;
+// CraftBukkit end
+
 public class EntitySnowman extends EntityGolem implements IRangedAttackMob
 {
     private static final String __OBFID = "CL_00001650";
@@ -61,7 +67,7 @@
 
         if (this.worldObj.getBiomeGenForCoords(i, k).getFloatTemperature(i, j, k) > 1.0F)
         {
-            this.attackEntityFrom(DamageSource.onFire, 1.0F);
+            this.attackEntityFrom(CraftEventFactory.MELTING, 1.0F); // CraftBukkit - DamageSource.BURN -> CraftEventFactory.MELTING
         }
 
         for (int l = 0; l < 4; ++l)
@@ -72,7 +78,18 @@
 
             if (this.worldObj.getBlock(i, j, k).getMaterial() == Material.air && this.worldObj.getBiomeGenForCoords(i, k).getFloatTemperature(i, j, k) < 0.8F && Blocks.snow_layer.canPlaceBlockAt(this.worldObj, i, j, k))
             {
-                this.worldObj.setBlock(i, j, k, Blocks.snow_layer);
+                // CraftBukkit start
+                org.bukkit.block.BlockState blockState = this.worldObj.getWorld().getBlockAt(i, j, k).getState();
+                blockState.setType(CraftMagicNumbers.getMaterial(Blocks.snow_layer));
+                EntityBlockFormEvent event = new EntityBlockFormEvent(this.getBukkitEntity(), blockState.getBlock(), blockState);
+                this.worldObj.getServer().getPluginManager().callEvent(event);
+
+                if (!event.isCancelled())
+                {
+                    blockState.update(true);
+                }
+
+                // CraftBukkit end
             }
         }
     }
