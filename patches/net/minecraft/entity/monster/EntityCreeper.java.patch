--- ../src-base/minecraft/net/minecraft/entity/monster/EntityCreeper.java
+++ ../src-work/minecraft/net/minecraft/entity/monster/EntityCreeper.java
@@ -23,6 +23,11 @@
 import net.minecraft.util.DamageSource;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.ExplosionPrimeEvent;
+// CraftBukkit end
+
 public class EntityCreeper extends EntityMob
 {
     private int lastActiveTime;
@@ -207,9 +212,35 @@
     public void onStruckByLightning(EntityLightningBolt p_70077_1_)
     {
         super.onStruckByLightning(p_70077_1_);
-        this.dataWatcher.updateObject(17, Byte.valueOf((byte)1));
+
+        // Cauldron start
+        if (p_70077_1_ != null)
+        {
+            // CraftBukkit start
+            if (CraftEventFactory.callCreeperPowerEvent(this, p_70077_1_, org.bukkit.event.entity.CreeperPowerEvent.PowerCause.LIGHTNING).isCancelled())
+            {
+                return;
+            }
+        }
+        // Cauldron end
+
+        this.setPowered(true);
     }
 
+    public void setPowered(boolean powered)
+    {
+        if (!powered)
+        {
+            this.dataWatcher.updateObject(17, Byte.valueOf((byte) 0));
+        }
+        else
+        {
+            this.dataWatcher.updateObject(17, Byte.valueOf((byte) 1));
+        }
+
+        // CraftBukkit end
+    }
+
     protected boolean interact(EntityPlayer p_70085_1_)
     {
         ItemStack itemstack = p_70085_1_.inventory.getCurrentItem();
@@ -235,17 +266,22 @@
         if (!this.worldObj.isRemote)
         {
             boolean flag = this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing");
+            // CraftBukkit start
+            float radius = this.getPowered() ? 6.0F : 3.0F;
+            ExplosionPrimeEvent event = new ExplosionPrimeEvent(this.getBukkitEntity(), radius, false);
+            this.worldObj.getServer().getPluginManager().callEvent(event);
 
-            if (this.getPowered())
+            if (!event.isCancelled())
             {
-                this.worldObj.createExplosion(this, this.posX, this.posY, this.posZ, (float)(this.explosionRadius * 2), flag);
+                this.worldObj.newExplosion(this, this.posX, this.posY, this.posZ, event.getRadius(), event.getFire(), flag);
+                this.setDead();
             }
             else
             {
-                this.worldObj.createExplosion(this, this.posX, this.posY, this.posZ, (float)this.explosionRadius, flag);
+                this.timeSinceIgnited = 0;
             }
 
-            this.setDead();
+            // CraftBukkit end
         }
     }
 
