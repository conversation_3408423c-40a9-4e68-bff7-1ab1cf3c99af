--- ../src-base/minecraft/net/minecraft/entity/monster/EntitySkeleton.java
+++ ../src-work/minecraft/net/minecraft/entity/monster/EntitySkeleton.java
@@ -36,6 +36,9 @@
 import net.minecraft.world.World;
 import net.minecraft.world.WorldProviderHell;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityCombustEvent; // CraftBukkit
+
 public class EntitySkeleton extends EntityMob implements IRangedAttackMob
 {
     private EntityAIArrowAttack aiArrowAttack = new EntityAIArrowAttack(this, 1.0D, 20, 60, 15.0F);
@@ -148,7 +151,16 @@
 
                 if (flag)
                 {
-                    this.setFire(8);
+                    // CraftBukkit start
+                    EntityCombustEvent event = new EntityCombustEvent(this.getBukkitEntity(), 8);
+                    this.worldObj.getServer().getPluginManager().callEvent(event);
+
+                    if (!event.isCancelled())
+                    {
+                        this.setFire(event.getDuration());
+                    }
+
+                    // CraftBukkit end
                 }
             }
         }
@@ -312,8 +324,23 @@
             entityarrow.setFire(100);
         }
 
+        // CraftBukkit start
+        org.bukkit.event.entity.EntityShootBowEvent event = CraftEventFactory.callEntityShootBowEvent(this, this.getHeldItem(), entityarrow, 0.8F);
+
+        if (event.isCancelled())
+        {
+            event.getProjectile().remove();
+            return;
+        }
+
+        if (event.getProjectile() == entityarrow.getBukkitEntity())
+        {
+            worldObj.spawnEntityInWorld(entityarrow);
+        }
+
+        // CraftBukkit end
         this.playSound("random.bow", 1.0F, 1.0F / (this.getRNG().nextFloat() * 0.4F + 0.8F));
-        this.worldObj.spawnEntityInWorld(entityarrow);
+        // this.worldObj.spawnEntityInWorld(entityarrow); // CraftBukkit - moved up
     }
 
     public int getSkeletonType()
