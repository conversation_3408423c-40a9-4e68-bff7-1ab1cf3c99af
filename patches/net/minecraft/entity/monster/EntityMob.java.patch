--- ../src-base/minecraft/net/minecraft/entity/monster/EntityMob.java
+++ ../src-work/minecraft/net/minecraft/entity/monster/EntityMob.java
@@ -1,5 +1,8 @@
 package net.minecraft.entity.monster;
 
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityTargetEvent; // CraftBukkit
 import net.minecraft.enchantment.EnchantmentHelper;
 import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityCreature;
@@ -75,7 +78,29 @@
             {
                 if (entity != this)
                 {
-                    this.entityToAttack = entity;
+                    // CraftBukkit start - We still need to call events for entities without goals
+                    if (entity != this.entityToAttack && (this instanceof EntityBlaze || this instanceof EntityEnderman || this instanceof EntitySpider || this instanceof EntityGiantZombie || this instanceof EntitySilverfish))
+                    {
+                        EntityTargetEvent event = CraftEventFactory.callEntityTargetEvent(this, entity, EntityTargetEvent.TargetReason.TARGET_ATTACKED_ENTITY);
+
+                        if (!event.isCancelled())
+                        {
+                            if (event.getTarget() == null)
+                            {
+                                this.entityToAttack = null;
+                            }
+                            else
+                            {
+                                this.entityToAttack = ((CraftEntity) event.getTarget()).getHandle();
+                            }
+                        }
+                    }
+                    else
+                    {
+                        this.entityToAttack = entity;
+                    }
+
+                    // CraftBukkit end
                 }
 
                 return true;
