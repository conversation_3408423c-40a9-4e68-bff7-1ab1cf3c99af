--- ../src-base/minecraft/net/minecraft/entity/boss/EntityWither.java
+++ ../src-work/minecraft/net/minecraft/entity/boss/EntityWither.java
@@ -5,7 +5,6 @@
 import java.util.Iterator;
 import java.util.List;
 import net.minecraft.block.Block;
-import net.minecraft.block.material.Material;
 import net.minecraft.command.IEntitySelector;
 import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLiving;
@@ -34,6 +33,11 @@
 import net.minecraft.world.EnumDifficulty;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.ExplosionPrimeEvent;
+// CraftBukkit end
+
 public class EntityWither extends EntityMob implements IBossDisplayData, IRangedAttackMob
 {
     private float[] field_82220_d = new float[2];
@@ -228,15 +232,25 @@
 
             if (i <= 0)
             {
-                this.worldObj.newExplosion(this, this.posX, this.posY + (double)this.getEyeHeight(), this.posZ, 7.0F, false, this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"));
-                this.worldObj.playBroadcastSound(1013, (int)this.posX, (int)this.posY, (int)this.posZ, 0);
+                // CraftBukkit start
+                ExplosionPrimeEvent event = new ExplosionPrimeEvent(this.getBukkitEntity(), 7.0F, false);
+                this.worldObj.getServer().getPluginManager().callEvent(event);
+
+                if (!event.isCancelled())
+                {
+                    this.worldObj.newExplosion(this, this.posX, this.posY + (double) this.getEyeHeight(), this.posZ, event.getRadius(), event.getFire(), this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"));
+                }
+
+                // CraftBukkit end
+                this.worldObj.newExplosion(this, this.posX, this.posY + (double) this.getEyeHeight(), this.posZ, 7.0F, false, this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"));
+                this.worldObj.playBroadcastSound(1013, (int) this.posX, (int) this.posY, (int) this.posZ, 0);
             }
 
             this.func_82215_s(i);
 
             if (this.ticksExisted % 10 == 0)
             {
-                this.heal(10.0F);
+                this.heal(10.0F, org.bukkit.event.entity.EntityRegainHealthEvent.RegainReason.WITHER_SPAWN); // CraftBukkit
             }
         }
         else
@@ -349,6 +363,13 @@
 
                                 if (!block.isAir(worldObj, j2, k, l) && block.canEntityDestroy(worldObj, j2, k, l, this))
                                 {
+                                    // CraftBukkit start
+                                    if (CraftEventFactory.callEntityChangeBlockEvent(this, j2, k, l, Blocks.air, 0).isCancelled())
+                                    {
+                                        continue;
+                                    }
+
+                                    // CraftBukkit end
                                     flag = this.worldObj.func_147480_a(j2, k, l, true) || flag;
                                 }
                             }
