--- ../src-base/minecraft/net/minecraft/entity/boss/EntityDragon.java
+++ ../src-work/minecraft/net/minecraft/entity/boss/EntityDragon.java
@@ -4,7 +4,6 @@
 import java.util.List;
 import net.minecraft.block.Block;
 import net.minecraft.block.BlockEndPortal;
-import net.minecraft.block.material.Material;
 import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLiving;
 import net.minecraft.entity.EntityLivingBase;
@@ -22,6 +21,22 @@
 import net.minecraft.world.Explosion;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import net.minecraft.entity.player.EntityPlayerMP;
+import net.minecraft.network.play.server.S23PacketBlockChange;
+import org.bukkit.block.BlockState;
+import org.bukkit.craftbukkit.v1_7_R4.CraftWorld;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.util.BlockStateListPopulator;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.craftbukkit.v1_7_R4.util.CraftMagicNumbers;
+import org.bukkit.event.entity.EntityCreatePortalEvent;
+import org.bukkit.event.entity.EntityExplodeEvent;
+import org.bukkit.event.entity.EntityRegainHealthEvent;
+import org.bukkit.event.entity.EntityTargetEvent;
+import org.bukkit.Bukkit;
+// CraftBukkit end
+
 public class EntityDragon extends EntityLiving implements IBossDisplayData, IEntityMultiPart, IMob
 {
     public double targetX;
@@ -44,6 +59,7 @@
     private Entity target;
     public int deathTicks;
     public EntityEnderCrystal healingEnderCrystal;
+    private Explosion explosionSource = new Explosion(null, this, Double.NaN, Double.NaN, Double.NaN, Float.NaN); // CraftBukkit - reusable source for CraftTNTPrimed.getSource()
     private static final String __OBFID = "CL_00001659";
 
     public EntityDragon(World p_i1700_1_)
@@ -355,14 +371,25 @@
             {
                 if (!this.worldObj.isRemote)
                 {
-                    this.attackEntityFromPart(this.dragonPartHead, DamageSource.setExplosionSource((Explosion)null), 10.0F);
+                    CraftEventFactory.entityDamage = this.healingEnderCrystal; // CraftBukkit
+                    this.attackEntityFromPart(this.dragonPartHead, DamageSource.setExplosionSource((Explosion) null), 10.0F);
+                    CraftEventFactory.entityDamage = null; // CraftBukkit
                 }
 
                 this.healingEnderCrystal = null;
             }
             else if (this.ticksExisted % 10 == 0 && this.getHealth() < this.getMaxHealth())
             {
-                this.setHealth(this.getHealth() + 1.0F);
+                // CraftBukkit start
+                EntityRegainHealthEvent event = new EntityRegainHealthEvent(this.getBukkitEntity(), 1.0D, EntityRegainHealthEvent.RegainReason.ENDER_CRYSTAL);
+                this.worldObj.getServer().getPluginManager().callEvent(event);
+
+                if (!event.isCancelled())
+                {
+                    this.setHealth((float)(this.getHealth() + event.getAmount()));
+                }
+
+                // CraftBukkit end
             }
         }
 
@@ -429,7 +456,24 @@
 
         if (this.rand.nextInt(2) == 0 && !this.worldObj.playerEntities.isEmpty())
         {
-            this.target = (Entity)this.worldObj.playerEntities.get(this.rand.nextInt(this.worldObj.playerEntities.size()));
+            // CraftBukkit start
+            Entity target = (Entity) this.worldObj.playerEntities.get(this.rand.nextInt(this.worldObj.playerEntities.size()));
+            EntityTargetEvent event = new EntityTargetEvent(this.getBukkitEntity(), target.getBukkitEntity(), EntityTargetEvent.TargetReason.RANDOM_TARGET);
+            this.worldObj.getServer().getPluginManager().callEvent(event);
+
+            if (!event.isCancelled())
+            {
+                if (event.getTarget() == null)
+                {
+                    this.target = null;
+                }
+                else
+                {
+                    this.target = ((CraftEntity) event.getTarget()).getHandle();
+                }
+            }
+
+            // CraftBukkit end
         }
         else
         {
@@ -468,6 +512,10 @@
         int j1 = MathHelper.floor_double(p_70972_1_.maxZ);
         boolean flag = false;
         boolean flag1 = false;
+        // CraftBukkit start - Create a list to hold all the destroyed blocks
+        List<org.bukkit.block.Block> destroyedBlocks = new java.util.ArrayList<org.bukkit.block.Block>();
+        CraftWorld craftWorld = this.worldObj.getWorld();
+        // CraftBukkit end
 
         for (int k1 = i; k1 <= l; ++k1)
         {
@@ -481,7 +529,11 @@
                     {
                         if (block.canEntityDestroy(worldObj, k1, l1, i2, this) && this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"))
                         {
-                            flag1 = this.worldObj.setBlockToAir(k1, l1, i2) || flag1;
+                            // CraftBukkit start - Add blocks to list rather than destroying them
+                            // flag1 = this.world.setAir(k1, l1, i2) || flag1;
+                            flag1 = true;
+                            destroyedBlocks.add(craftWorld.getBlockAt(k1, l1, i2));
+                            // CraftBukkit end
                         }
                         else
                         {
@@ -494,6 +546,52 @@
 
         if (flag1)
         {
+            // CraftBukkit start - Set off an EntityExplodeEvent for the dragon exploding all these blocks
+            org.bukkit.entity.Entity bukkitEntity = this.getBukkitEntity();
+            EntityExplodeEvent event = new EntityExplodeEvent(bukkitEntity, bukkitEntity.getLocation(), destroyedBlocks, 0F);
+            Bukkit.getPluginManager().callEvent(event);
+
+            if (event.isCancelled())
+            {
+                // This flag literally means 'Dragon hit something hard' (Obsidian, White Stone or Bedrock) and will cause the dragon to slow down.
+                // We should consider adding an event extension for it, or perhaps returning true if the event is cancelled.
+                return flag;
+            }
+            else if (event.getYield() == 0F)
+            {
+                // Yield zero ==> no drops
+                for (org.bukkit.block.Block block : event.blockList())
+                {
+                    this.worldObj.setBlockToAir(block.getX(), block.getY(), block.getZ());
+                }
+            }
+            else
+            {
+                for (org.bukkit.block.Block block : event.blockList())
+                {
+                    org.bukkit.Material blockId = block.getType();
+
+                    if (blockId == org.bukkit.Material.AIR)
+                    {
+                        continue;
+                    }
+
+                    int blockX = block.getX();
+                    int blockY = block.getY();
+                    int blockZ = block.getZ();
+                    Block nmsBlock = CraftMagicNumbers.getBlock(blockId);
+
+                    if (nmsBlock.canDropFromExplosion(explosionSource))
+                    {
+                        nmsBlock.dropBlockAsItemWithChance(this.worldObj, blockX, blockY, blockZ, block.getData(), event.getYield(), 0);
+                    }
+
+                    nmsBlock.onBlockDestroyedByExplosion(worldObj, blockX, blockY, blockZ, explosionSource);
+                    this.worldObj.setBlockToAir(blockX, blockY, blockZ);
+                }
+            }
+
+            // CraftBukkit end
             double d1 = p_70972_1_.minX + (p_70972_1_.maxX - p_70972_1_.minX) * (double)this.rand.nextFloat();
             double d2 = p_70972_1_.minY + (p_70972_1_.maxY - p_70972_1_.minY) * (double)this.rand.nextFloat();
             double d0 = p_70972_1_.minZ + (p_70972_1_.maxZ - p_70972_1_.minZ) * (double)this.rand.nextFloat();
@@ -531,13 +629,18 @@
         return false;
     }
 
-    protected boolean func_82195_e(DamageSource p_82195_1_, float p_82195_2_)
+    public boolean func_82195_e(DamageSource p_82195_1_, float p_82195_2_) // CraftBukkit - protected -> public
     {
         return super.attackEntityFrom(p_82195_1_, p_82195_2_);
     }
 
     protected void onDeathUpdate()
     {
+        if (this.isDead)
+        {
+            return;    // CraftBukkit - can't kill what's already dead
+        }
+
         ++this.deathTicks;
 
         if (this.deathTicks >= 180 && this.deathTicks <= 200)
@@ -555,7 +658,7 @@
         {
             if (this.deathTicks > 150 && this.deathTicks % 5 == 0)
             {
-                i = 1000;
+                i = this.expToDrop / 12; // CraftBukkit - drop experience as dragon falls from sky. use experience drop from death event. This is now set in getExpReward()
 
                 while (i > 0)
                 {
@@ -576,7 +679,7 @@
 
         if (this.deathTicks == 200 && !this.worldObj.isRemote)
         {
-            i = 2000;
+            i = this.expToDrop - (10 * this.expToDrop / 12); // CraftBukkit - drop the remaining experience
 
             while (i > 0)
             {
@@ -595,6 +698,8 @@
         byte b0 = 64;
         BlockEndPortal.field_149948_a = true;
         byte b1 = 4;
+        // CraftBukkit start - Replace any "this.world" in the following with just "world"!
+        BlockStateListPopulator world = new BlockStateListPopulator(this.worldObj.getWorld());
 
         for (int k = b0 - 1; k <= b0 + 32; ++k)
         {
@@ -641,6 +746,35 @@
         this.worldObj.setBlock(p_70975_1_, b0 + 2, p_70975_2_ + 1, Blocks.torch);
         this.worldObj.setBlock(p_70975_1_, b0 + 3, p_70975_2_, Blocks.bedrock);
         this.worldObj.setBlock(p_70975_1_, b0 + 4, p_70975_2_, Blocks.dragon_egg);
+        EntityCreatePortalEvent event = new EntityCreatePortalEvent((org.bukkit.entity.LivingEntity) this.getBukkitEntity(), java.util.Collections.unmodifiableList(world.getList()), org.bukkit.PortalType.ENDER);
+        this.worldObj.getServer().getPluginManager().callEvent(event);
+
+        if (!event.isCancelled())
+        {
+            for (BlockState state : event.getBlocks())
+            {
+                state.update(true);
+            }
+        }
+        else
+        {
+            for (BlockState state : event.getBlocks())
+            {
+                S23PacketBlockChange packet = new S23PacketBlockChange(state.getX(), state.getY(), state.getZ(), this.worldObj);
+
+                for (Iterator it = this.worldObj.playerEntities.iterator(); it.hasNext();)
+                {
+                    EntityPlayer entity = (EntityPlayer) it.next();
+
+                    if (entity instanceof EntityPlayerMP)
+                    {
+                        ((EntityPlayerMP) entity).playerNetServerHandler.sendPacket(packet);
+                    }
+                }
+            }
+        }
+
+        // CraftBukkit end
         BlockEndPortal.field_149948_a = false;
     }
 
@@ -675,4 +809,13 @@
     {
         return 5.0F;
     }
+
+    // CraftBukkit start
+    public int getExpReward()
+    {
+        // This value is equal to the amount of experience dropped while falling from the sky (10 * 1000)
+        // plus what is dropped when the dragon hits the ground (2000)
+        return 12000;
+    }
+    // CraftBukkit end
 }
