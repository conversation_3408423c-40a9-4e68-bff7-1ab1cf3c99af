--- ../src-base/minecraft/net/minecraft/entity/player/PlayerCapabilities.java
+++ ../src-work/minecraft/net/minecraft/entity/player/PlayerCapabilities.java
@@ -11,8 +11,8 @@
     public boolean allowFlying;
     public boolean isCreativeMode;
     public boolean allowEdit = true;
-    private float flySpeed = 0.05F;
-    private float walkSpeed = 0.1F;
+    public float flySpeed = 0.05F; // CraftBukkit private -> public
+    public float walkSpeed = 0.1F; // CraftBukkit private -> public
     private static final String __OBFID = "CL_00001708";
 
     public void writeCapabilitiesToNBT(NBTTagCompound p_75091_1_)
