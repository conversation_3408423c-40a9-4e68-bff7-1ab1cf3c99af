--- ../src-base/minecraft/net/minecraft/entity/passive/EntitySheep.java
+++ ../src-work/minecraft/net/minecraft/entity/passive/EntitySheep.java
@@ -32,6 +32,12 @@
 import net.minecraft.world.World;
 import net.minecraftforge.common.IShearable;
 
+// CraftBukkit start
+import net.minecraft.inventory.InventoryCraftResult;
+import org.bukkit.event.entity.SheepRegrowWoolEvent;
+import org.bukkit.event.player.PlayerShearEntityEvent;
+// CraftBukkit end
+
 public class EntitySheep extends EntityAnimal implements IShearable
 {
     private final InventoryCrafting field_90016_e = new InventoryCrafting(new Container()
@@ -63,6 +69,7 @@
         this.tasks.addTask(8, new EntityAILookIdle(this));
         this.field_90016_e.setInventorySlotContents(0, new ItemStack(Items.dye, 1, 0));
         this.field_90016_e.setInventorySlotContents(1, new ItemStack(Items.dye, 1, 0));
+        this.field_90016_e.resultInventory = new InventoryCraftResult(); // CraftBukkit - add result slot for event
     }
 
     protected boolean isAIEnabled()
@@ -231,8 +238,17 @@
 
     public void eatGrassBonus()
     {
-        this.setSheared(false);
+        // CraftBukkit start
+        SheepRegrowWoolEvent event = new SheepRegrowWoolEvent((org.bukkit.entity.Sheep) this.getBukkitEntity());
+        this.worldObj.getServer().getPluginManager().callEvent(event);
 
+        if (!event.isCancelled())
+        {
+            this.setSheared(false);
+        }
+
+        // CraftBukkit end
+
         if (this.isChild())
         {
             this.addGrowth(60);
