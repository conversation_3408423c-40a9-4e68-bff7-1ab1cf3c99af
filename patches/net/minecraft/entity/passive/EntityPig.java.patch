--- ../src-base/minecraft/net/minecraft/entity/passive/EntityPig.java
+++ ../src-work/minecraft/net/minecraft/entity/passive/EntityPig.java
@@ -22,6 +22,8 @@
 import net.minecraft.stats.AchievementList;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory; // CraftBukkit
+
 public class EntityPig extends EntityAnimal
 {
     private final EntityAIControlledByPlayer aiControlledByPlayer;
@@ -171,9 +173,23 @@
         if (!this.worldObj.isRemote)
         {
             EntityPigZombie entitypigzombie = new EntityPigZombie(this.worldObj);
+
+            // Cauldron start
+            if (p_70077_1_ != null)
+            {
+                // CraftBukkit start
+                if (CraftEventFactory.callPigZapEvent(this, p_70077_1_, entitypigzombie).isCancelled())
+                {
+                    return;
+                }
+
+                // CraftBukkit end
+            }
+            // Cauldron end
             entitypigzombie.setCurrentItemOrArmor(0, new ItemStack(Items.golden_sword));
             entitypigzombie.setLocationAndAngles(this.posX, this.posY, this.posZ, this.rotationYaw, this.rotationPitch);
-            this.worldObj.spawnEntityInWorld(entitypigzombie);
+            // CraftBukkit - added a reason for spawning this creature
+            this.worldObj.addEntity(entitypigzombie, org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.LIGHTNING);
             this.setDead();
         }
     }
