--- ../src-base/minecraft/net/minecraft/entity/passive/EntitySquid.java
+++ ../src-work/minecraft/net/minecraft/entity/passive/EntitySquid.java
@@ -1,6 +1,5 @@
 package net.minecraft.entity.passive;
 
-import net.minecraft.block.material.Material;
 import net.minecraft.entity.SharedMonsterAttributes;
 import net.minecraft.init.Items;
 import net.minecraft.item.Item;
@@ -8,6 +7,8 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.TrigMath; // CraftBukkit
+
 public class EntitySquid extends EntityWaterMob
 {
     public float squidPitch;
@@ -79,10 +80,12 @@
         }
     }
 
+    /* CraftBukkit start - Delegate to Entity to use existing inWater value
     public boolean isInWater()
     {
         return this.worldObj.handleMaterialAcceleration(this.boundingBox.expand(0.0D, -0.6000000238418579D, 0.0D), Material.water, this);
     }
+    // CraftBukkit end */
 
     public void onLivingUpdate()
     {
@@ -137,10 +140,12 @@
             }
 
             f = MathHelper.sqrt_double(this.motionX * this.motionX + this.motionZ * this.motionZ);
-            this.renderYawOffset += (-((float)Math.atan2(this.motionX, this.motionZ)) * 180.0F / (float)Math.PI - this.renderYawOffset) * 0.1F;
+            // CraftBukkit - Math -> TrigMath
+            this.renderYawOffset += (-((float) TrigMath.atan2(this.motionX, this.motionZ)) * 180.0F / (float)Math.PI - this.renderYawOffset) * 0.1F;
             this.rotationYaw = this.renderYawOffset;
             this.squidYaw += (float)Math.PI * this.field_70871_bB * 1.5F;
-            this.squidPitch += (-((float)Math.atan2((double)f, this.motionY)) * 180.0F / (float)Math.PI - this.squidPitch) * 0.1F;
+            // CraftBukkit - Math -> TrigMath
+            this.squidPitch += (-((float) TrigMath.atan2((double) f, this.motionY)) * 180.0F / (float)Math.PI - this.squidPitch) * 0.1F;
         }
         else
         {
