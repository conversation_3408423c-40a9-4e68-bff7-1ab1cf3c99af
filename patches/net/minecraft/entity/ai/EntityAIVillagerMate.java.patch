--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIVillagerMate.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIVillagerMate.java
@@ -119,7 +119,7 @@
         this.villagerObj.setGrowingAge(6000);
         entityvillager.setGrowingAge(-24000);
         entityvillager.setLocationAndAngles(this.villagerObj.posX, this.villagerObj.posY, this.villagerObj.posZ, 0.0F, 0.0F);
-        this.worldObj.spawnEntityInWorld(entityvillager);
+        this.worldObj.addEntity(entityvillager, org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.BREEDING); // CraftBukkit - added SpawnReason
         this.worldObj.setEntityState(entityvillager, (byte)12);
     }
 }
