--- ../src-base/minecraft/net/minecraft/entity/ai/EntityLookHelper.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityLookHelper.java
@@ -5,6 +5,8 @@
 import net.minecraft.entity.EntityLivingBase;
 import net.minecraft.util.MathHelper;
 
+import org.bukkit.craftbukkit.v1_7_R4.TrigMath; // CraftBukkit
+
 public class EntityLookHelper
 {
     private EntityLiving entity;
@@ -61,8 +63,10 @@
             double d1 = this.posY - (this.entity.posY + (double)this.entity.getEyeHeight());
             double d2 = this.posZ - this.entity.posZ;
             double d3 = (double)MathHelper.sqrt_double(d0 * d0 + d2 * d2);
-            float f = (float)(Math.atan2(d2, d0) * 180.0D / Math.PI) - 90.0F;
-            float f1 = (float)(-(Math.atan2(d1, d3) * 180.0D / Math.PI));
+            // CraftBukkit start - Math -> TrigMath
+            float f = (float)(TrigMath.atan2(d2, d0) * 180.0D / Math.PI) - 90.0F;
+            float f1 = (float)(-(TrigMath.atan2(d1, d3) * 180.0D / Math.PI));
+            // CraftBukkit end
             this.entity.rotationPitch = this.updateRotation(this.entity.rotationPitch, f1, this.deltaLookPitch);
             this.entity.rotationYawHead = this.updateRotation(this.entity.rotationYawHead, f, this.deltaLookYaw);
         }
