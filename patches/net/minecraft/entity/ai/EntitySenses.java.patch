--- ../src-base/minecraft/net/minecraft/entity/ai/EntitySenses.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntitySenses.java
@@ -1,6 +1,7 @@
 package net.minecraft.entity.ai;
 
 import java.util.ArrayList;
+import java.util.HashSet;
 import java.util.List;
 import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLiving;
@@ -8,8 +9,8 @@
 public class EntitySenses
 {
     EntityLiving entityObj;
-    List seenEntities = new ArrayList();
-    List unseenEntities = new ArrayList();
+    HashSet<Entity> seenEntities = new HashSet<Entity>(); // Thermos convert sensing to HashSet for faster sensing checks.
+    HashSet<Entity> unseenEntities = new HashSet<Entity>();
     private static final String __OBFID = "CL_00001628";
 
     public EntitySenses(EntityLiving p_i1672_1_)
