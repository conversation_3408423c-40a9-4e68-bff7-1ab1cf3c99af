--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIMate.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIMate.java
@@ -4,6 +4,7 @@
 import java.util.List;
 import java.util.Random;
 import net.minecraft.entity.EntityAgeable;
+import net.minecraft.entity.EntityLiving;
 import net.minecraft.entity.item.EntityXPOrb;
 import net.minecraft.entity.passive.EntityAnimal;
 import net.minecraft.entity.passive.EntityCow;
@@ -12,6 +13,8 @@
 import net.minecraft.stats.StatList;
 import net.minecraft.world.World;
 
+import net.minecraft.entity.passive.EntityTameable; // CraftBukkit
+
 public class EntityAIMate extends EntityAIBase
 {
     private EntityAnimal theAnimal;
@@ -93,6 +96,13 @@
 
         if (entityageable != null)
         {
+            // CraftBukkit start - set persistence for tame animals
+            if (entityageable instanceof EntityTameable && ((EntityTameable) entityageable).isTamed())
+            {
+                ((EntityLiving)entityageable).persistenceRequired = true; // Cauldron - fix illegal access error. SS bug?
+            }
+
+            // CraftBukkit end
             EntityPlayer entityplayer = this.theAnimal.func_146083_cb();
 
             if (entityplayer == null && this.targetMate.func_146083_cb() != null)
@@ -116,7 +126,7 @@
             this.targetMate.resetInLove();
             entityageable.setGrowingAge(-24000);
             entityageable.setLocationAndAngles(this.theAnimal.posX, this.theAnimal.posY, this.theAnimal.posZ, 0.0F, 0.0F);
-            this.theWorld.spawnEntityInWorld(entityageable);
+            this.theWorld.addEntity(entityageable, org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.BREEDING); // CraftBukkit - added SpawnReason
             Random random = this.theAnimal.getRNG();
 
             for (int i = 0; i < 7; ++i)
