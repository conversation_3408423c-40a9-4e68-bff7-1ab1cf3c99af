--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIEatGrass.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIEatGrass.java
@@ -6,6 +6,11 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.Material;
+// CraftBukkit end
+
 public class EntityAIEatGrass extends EntityAIBase
 {
     private EntityLiving field_151500_b;
@@ -69,7 +74,8 @@
 
             if (this.field_151501_c.getBlock(i, j, k) == Blocks.tallgrass)
             {
-                if (this.field_151501_c.getGameRules().getGameRuleBooleanValue("mobGriefing"))
+                // CraftBukkit
+                if (!CraftEventFactory.callEntityChangeBlockEvent(this.field_151500_b, this.field_151500_b.worldObj.getWorld().getBlockAt(i, j, k), Material.AIR, !this.field_151501_c.getGameRules().getGameRuleBooleanValue("mobGriefing")).isCancelled())
                 {
                     this.field_151501_c.func_147480_a(i, j, k, false);
                 }
@@ -78,7 +84,8 @@
             }
             else if (this.field_151501_c.getBlock(i, j - 1, k) == Blocks.grass)
             {
-                if (this.field_151501_c.getGameRules().getGameRuleBooleanValue("mobGriefing"))
+                // CraftBukkit
+                if (!CraftEventFactory.callEntityChangeBlockEvent(this.field_151500_b, this.field_151500_b.worldObj.getWorld().getBlockAt(i, j - 1, k), Material.DIRT, !this.field_151501_c.getGameRules().getGameRuleBooleanValue("mobGriefing")).isCancelled())
                 {
                     this.field_151501_c.playAuxSFX(2001, i, j - 1, k, Block.getIdFromBlock(Blocks.grass));
                     this.field_151501_c.setBlock(i, j - 1, k, Blocks.dirt, 0, 2);
