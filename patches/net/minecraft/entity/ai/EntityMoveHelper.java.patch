--- ../src-base/minecraft/net/minecraft/entity/ai/EntityMoveHelper.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityMoveHelper.java
@@ -3,6 +3,7 @@
 import net.minecraft.entity.EntityLiving;
 import net.minecraft.entity.SharedMonsterAttributes;
 import net.minecraft.util.MathHelper;
+import org.bukkit.craftbukkit.v1_7_R4.TrigMath;
 
 public class EntityMoveHelper
 {
@@ -56,7 +57,8 @@
 
             if (d3 >= 2.500000277905201E-7D)
             {
-                float f = (float)(Math.atan2(d1, d0) * 180.0D / Math.PI) - 90.0F;
+                // CraftBukkit - Math -> TrigMath
+                float f = (float)(TrigMath.atan2(d1, d0) * 180.0D / Math.PI) - 90.0F;
                 this.entity.rotationYaw = this.limitAngle(this.entity.rotationYaw, f, 30.0F);
                 this.entity.setAIMoveSpeed((float)(this.speed * this.entity.getEntityAttribute(SharedMonsterAttributes.movementSpeed).getAttributeValue()));
 
