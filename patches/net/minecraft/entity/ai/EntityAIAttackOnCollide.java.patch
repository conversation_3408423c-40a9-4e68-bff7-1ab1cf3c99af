--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIAttackOnCollide.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIAttackOnCollide.java
@@ -7,6 +7,11 @@
 import net.minecraft.util.MathHelper;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityTargetEvent;
+// CraftBukkit end
+
 public class EntityAIAttackOnCollide extends EntityAIBase
 {
     World worldObj;
@@ -73,6 +78,15 @@
     public boolean continueExecuting()
     {
         EntityLivingBase entitylivingbase = this.attacker.getAttackTarget();
+        // CraftBukkit start
+        EntityTargetEvent.TargetReason reason = this.attacker.getAttackTarget() == null ? EntityTargetEvent.TargetReason.FORGOT_TARGET : EntityTargetEvent.TargetReason.TARGET_DIED;
+
+        if (this.attacker.getAttackTarget() == null || (this.attacker.getAttackTarget() != null && !this.attacker.getAttackTarget().isEntityAlive()))
+        {
+            CraftEventFactory.callEntityTargetEvent(attacker, null, reason);
+        }
+
+        // CraftBukkit end
         return entitylivingbase == null ? false : (!entitylivingbase.isEntityAlive() ? false : (!this.longMemory ? !this.attacker.getNavigator().noPath() : this.attacker.isWithinHomeDistance(MathHelper.floor_double(entitylivingbase.posX), MathHelper.floor_double(entitylivingbase.posY), MathHelper.floor_double(entitylivingbase.posZ))));
     }
 
