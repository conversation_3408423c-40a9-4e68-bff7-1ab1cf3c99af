--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIArrowAttack.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIArrowAttack.java
@@ -5,6 +5,12 @@
 import net.minecraft.entity.IRangedAttackMob;
 import net.minecraft.util.MathHelper;
 
+// CraftBukkit start
+import net.minecraft.entity.Entity;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityTargetEvent;
+// CraftBukkit end
+
 public class EntityAIArrowAttack extends EntityAIBase
 {
     private final EntityLiving entityHost;
@@ -67,6 +73,10 @@
 
     public void resetTask()
     {
+        // CraftBukkit start
+        EntityTargetEvent.TargetReason reason = this.attackTarget.isEntityAlive() ? EntityTargetEvent.TargetReason.FORGOT_TARGET : EntityTargetEvent.TargetReason.TARGET_DIED;
+        CraftEventFactory.callEntityTargetEvent((Entity) rangedAttackEntityHost, null, reason);
+        // CraftBukkit end
         this.attackTarget = null;
         this.field_75318_f = 0;
         this.rangedAttackTime = -1;
