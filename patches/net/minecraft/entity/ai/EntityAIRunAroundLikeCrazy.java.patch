--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIRunAroundLikeCrazy.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIRunAroundLikeCrazy.java
@@ -4,6 +4,7 @@
 import net.minecraft.entity.passive.EntityHorse;
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.util.Vec3;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class EntityAIRunAroundLikeCrazy extends EntityAIBase
 {
@@ -64,7 +65,8 @@
                 int i = this.horseHost.getTemper();
                 int j = this.horseHost.getMaxTemper();
 
-                if (j > 0 && this.horseHost.getRNG().nextInt(j) < i)
+                // CraftBukkit
+                if (j > 0 && this.horseHost.getRNG().nextInt(j) < i && !CraftEventFactory.callEntityTameEvent(this.horseHost, (EntityPlayer) this.horseHost.riddenByEntity).isCancelled() && this.horseHost.riddenByEntity instanceof EntityPlayer)
                 {
                     this.horseHost.setTamedBy((EntityPlayer)this.horseHost.riddenByEntity);
                     this.horseHost.worldObj.setEntityState(this.horseHost, (byte)7);
@@ -74,8 +76,20 @@
                 this.horseHost.increaseTemper(5);
             }
 
-            this.horseHost.riddenByEntity.mountEntity((Entity)null);
-            this.horseHost.riddenByEntity = null;
+            // CraftBukkit start - Handle dismounting to account for VehicleExitEvent being fired.
+            if (this.horseHost.riddenByEntity != null)
+            {
+                this.horseHost.riddenByEntity.mountEntity((Entity) null);
+
+                // If the entity still has a passenger, then a plugin cancelled the event.
+                if (this.horseHost.riddenByEntity != null)
+                {
+                    return;
+                }
+            }
+
+            // this.entity.passenger = null;
+            // CraftBukkit end
             this.horseHost.makeHorseRearWithSound();
             this.horseHost.worldObj.setEntityState(this.horseHost, (byte)6);
         }
