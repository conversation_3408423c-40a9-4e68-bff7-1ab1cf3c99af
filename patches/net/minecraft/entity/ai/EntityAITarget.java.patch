--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAITarget.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAITarget.java
@@ -12,6 +12,12 @@
 import net.minecraft.util.MathHelper;
 import org.apache.commons.lang3.StringUtils;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityTargetEvent;
+// CraftBukkit end
+
 public abstract class EntityAITarget extends EntityAIBase
 {
     protected EntityCreature taskOwner;
@@ -156,6 +162,51 @@
                     }
                 }
 
+                // CraftBukkit start - Check all the different target goals for the reason, default to RANDOM_TARGET
+                EntityTargetEvent.TargetReason reason = EntityTargetEvent.TargetReason.RANDOM_TARGET;
+
+                if (this instanceof EntityAIDefendVillage)
+                {
+                    reason = EntityTargetEvent.TargetReason.DEFEND_VILLAGE;
+                }
+                else if (this instanceof EntityAIHurtByTarget)
+                {
+                    reason = EntityTargetEvent.TargetReason.TARGET_ATTACKED_ENTITY;
+                }
+                else if (this instanceof EntityAINearestAttackableTarget)
+                {
+                    if (p_75296_1_ instanceof EntityPlayer)
+                    {
+                        reason = EntityTargetEvent.TargetReason.CLOSEST_PLAYER;
+                    }
+                }
+                else if (this instanceof EntityAIOwnerHurtByTarget)
+                {
+                    reason = EntityTargetEvent.TargetReason.TARGET_ATTACKED_OWNER;
+                }
+                else if (this instanceof EntityAIOwnerHurtTarget)
+                {
+                    reason = EntityTargetEvent.TargetReason.OWNER_ATTACKED_TARGET;
+                }
+
+                org.bukkit.event.entity.EntityTargetLivingEntityEvent event = CraftEventFactory.callEntityTargetLivingEvent(this.taskOwner, p_75296_1_, reason);
+
+                if (event.isCancelled() || event.getTarget() == null)
+                {
+                    this.taskOwner.setAttackTarget(null);
+                    return false;
+                }
+                else if (p_75296_1_.getBukkitEntity() != event.getTarget())
+                {
+                    this.taskOwner.setAttackTarget((EntityLivingBase)((CraftEntity) event.getTarget()).getHandle());
+                }
+
+                if (this.taskOwner instanceof EntityCreature)
+                {
+                    ((EntityCreature) this.taskOwner).entityToAttack = ((CraftEntity) event.getTarget()).getHandle();
+                }
+
+                // CraftBukkit end
                 return true;
             }
         }
