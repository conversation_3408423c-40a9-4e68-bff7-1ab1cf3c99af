--- ../src-base/minecraft/net/minecraft/entity/ai/EntityAIBreakDoor.java
+++ ../src-work/minecraft/net/minecraft/entity/ai/EntityAIBreakDoor.java
@@ -3,6 +3,7 @@
 import net.minecraft.block.Block;
 import net.minecraft.entity.EntityLiving;
 import net.minecraft.world.EnumDifficulty;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class EntityAIBreakDoor extends EntityAIDoorInteract
 {
@@ -58,6 +59,14 @@
 
         if (this.breakingTime == 240 && this.theEntity.worldObj.difficultySetting == EnumDifficulty.HARD)
         {
+            // CraftBukkit start
+            if (CraftEventFactory.callEntityBreakDoorEvent(this.theEntity, this.entityPosX, this.entityPosY, this.entityPosZ).isCancelled())
+            {
+                this.updateTask();
+                return;
+            }
+
+            // CraftBukkit end
             this.theEntity.worldObj.setBlockToAir(this.entityPosX, this.entityPosY, this.entityPosZ);
             this.theEntity.worldObj.playAuxSFX(1012, this.entityPosX, this.entityPosY, this.entityPosZ, 0);
             this.theEntity.worldObj.playAuxSFX(2001, this.entityPosX, this.entityPosY, this.entityPosZ, Block.getIdFromBlock(this.field_151504_e));
