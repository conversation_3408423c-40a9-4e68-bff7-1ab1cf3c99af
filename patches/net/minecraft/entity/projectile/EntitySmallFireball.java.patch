--- ../src-base/minecraft/net/minecraft/entity/projectile/EntitySmallFireball.java
+++ ../src-work/minecraft/net/minecraft/entity/projectile/EntitySmallFireball.java
@@ -6,6 +6,9 @@
 import net.minecraft.util.MovingObjectPosition;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.event.entity.EntityCombustByEntityEvent; // CraftBukkit
+
 public class EntitySmallFireball extends EntityFireball
 {
     private static final String __OBFID = "CL_00001721";
@@ -36,7 +39,16 @@
             {
                 if (!p_70227_1_.entityHit.isImmuneToFire() && p_70227_1_.entityHit.attackEntityFrom(DamageSource.causeFireballDamage(this, this.shootingEntity), 5.0F))
                 {
-                    p_70227_1_.entityHit.setFire(5);
+                    // CraftBukkit start - Entity damage by entity event + combust event
+                    EntityCombustByEntityEvent event = new EntityCombustByEntityEvent((org.bukkit.entity.Projectile) this.getBukkitEntity(), p_70227_1_.entityHit.getBukkitEntity(), 5);
+                    p_70227_1_.entityHit.worldObj.getServer().getPluginManager().callEvent(event);
+
+                    if (!event.isCancelled())
+                    {
+                        p_70227_1_.entityHit.setFire(event.getDuration());
+                    }
+
+                    // CraftBukkit end
                 }
             }
             else
@@ -68,7 +80,13 @@
 
                 if (this.worldObj.isAirBlock(i, j, k))
                 {
-                    this.worldObj.setBlock(i, j, k, Blocks.fire);
+                    // CraftBukkit start
+                    if (!CraftEventFactory.callBlockIgniteEvent(worldObj, i, j, k, this).isCancelled())
+                    {
+                        this.worldObj.setBlock(i, j, k, Blocks.fire);
+                    }
+
+                    // CraftBukkit end
                 }
             }
 
@@ -85,4 +103,11 @@
     {
         return false;
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
