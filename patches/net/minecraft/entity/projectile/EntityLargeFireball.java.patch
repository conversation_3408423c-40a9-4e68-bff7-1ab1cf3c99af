--- ../src-base/minecraft/net/minecraft/entity/projectile/EntityLargeFireball.java
+++ ../src-work/minecraft/net/minecraft/entity/projectile/EntityLargeFireball.java
@@ -2,13 +2,15 @@
 
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
-import net.minecraft.entity.Entity;
 import net.minecraft.entity.EntityLivingBase;
 import net.minecraft.nbt.NBTTagCompound;
 import net.minecraft.util.DamageSource;
 import net.minecraft.util.MovingObjectPosition;
 import net.minecraft.world.World;
 
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.event.entity.ExplosionPrimeEvent; // CraftBukkit
+
 public class EntityLargeFireball extends EntityFireball
 {
     public int field_92057_e = 1;
@@ -39,7 +41,17 @@
                 p_70227_1_.entityHit.attackEntityFrom(DamageSource.causeFireballDamage(this, this.shootingEntity), 6.0F);
             }
 
-            this.worldObj.newExplosion((Entity)null, this.posX, this.posY, this.posZ, (float)this.field_92057_e, true, this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"));
+            // CraftBukkit start
+            ExplosionPrimeEvent event = new ExplosionPrimeEvent((org.bukkit.entity.Explosive) CraftEntity.getEntity(this.worldObj.getServer(), this));
+            this.worldObj.getServer().getPluginManager().callEvent(event);
+
+            if (!event.isCancelled())
+            {
+                // give 'this' instead of (Entity) null so we know what causes the damage
+                this.worldObj.newExplosion(this, this.posX, this.posY, this.posZ, event.getRadius(), event.getFire(), this.worldObj.getGameRules().getGameRuleBooleanValue("mobGriefing"));
+            }
+
+            // CraftBukkit end
             this.setDead();
         }
     }
@@ -56,7 +68,8 @@
 
         if (p_70037_1_.hasKey("ExplosionPower", 99))
         {
-            this.field_92057_e = p_70037_1_.getInteger("ExplosionPower");
+            // CraftBukkit - set bukkitYield when setting explosionpower
+            this.bukkitYield = this.field_92057_e = p_70037_1_.getInteger("ExplosionPower");
         }
     }
 }
