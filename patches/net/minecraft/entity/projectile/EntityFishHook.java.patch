--- ../src-base/minecraft/net/minecraft/entity/projectile/EntityFishHook.java
+++ ../src-work/minecraft/net/minecraft/entity/projectile/EntityFishHook.java
@@ -27,6 +27,13 @@
 import net.minecraft.world.World;
 import net.minecraft.world.WorldServer;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.entity.Player;
+import org.bukkit.entity.Fish;
+import org.bukkit.event.player.PlayerFishEvent;
+// CraftBukkit end
+
 public class EntityFishHook extends Entity
 {
     public static final List field_146039_d = Arrays.asList(new WeightedRandomFishable[] {(new WeightedRandomFishable(new ItemStack(Items.leather_boots), 10)).func_150709_a(0.9F), new WeightedRandomFishable(new ItemStack(Items.leather), 10), new WeightedRandomFishable(new ItemStack(Items.bone), 10), new WeightedRandomFishable(new ItemStack(Items.potionitem), 10), new WeightedRandomFishable(new ItemStack(Items.string), 5), (new WeightedRandomFishable(new ItemStack(Items.fishing_rod), 2)).func_150709_a(0.9F), new WeightedRandomFishable(new ItemStack(Items.bowl), 10), new WeightedRandomFishable(new ItemStack(Items.stick), 5), new WeightedRandomFishable(new ItemStack(Items.dye, 10, 0), 1), new WeightedRandomFishable(new ItemStack(Blocks.tripwire_hook), 10), new WeightedRandomFishable(new ItemStack(Items.rotten_flesh), 10)});
@@ -258,7 +265,7 @@
 
                     if (movingobjectposition1 != null)
                     {
-                        d2 = vec31.distanceTo(movingobjectposition1.hitVec);
+                        d2 = vec31.squareDistanceTo(movingobjectposition1.hitVec); // CraftBukkit - distance efficiency
 
                         if (d2 < d0 || d0 == 0.0D)
                         {
@@ -276,6 +283,8 @@
 
             if (movingobjectposition != null)
             {
+                CraftEventFactory.callProjectileHitEvent(this); // Craftbukkit - Call event
+
                 if (movingobjectposition.entityHit != null)
                 {
                     if (movingobjectposition.entityHit.attackEntityFrom(DamageSource.causeThrownDamage(this, this.field_146042_b), 0.0F))
@@ -509,6 +518,16 @@
 
             if (this.field_146043_c != null)
             {
+                // CraftBukkit start
+                PlayerFishEvent playerFishEvent = new PlayerFishEvent((Player) this.field_146042_b.getBukkitEntity(), this.field_146043_c.getBukkitEntity(), (Fish) this.getBukkitEntity(), PlayerFishEvent.State.CAUGHT_ENTITY);
+                this.worldObj.getServer().getPluginManager().callEvent(playerFishEvent);
+
+                if (playerFishEvent.isCancelled())
+                {
+                    return 0;
+                }
+
+                // CraftBukkit end
                 double d0 = this.field_146042_b.posX - this.posX;
                 double d2 = this.field_146042_b.posY - this.posY;
                 double d4 = this.field_146042_b.posZ - this.posZ;
@@ -522,6 +541,17 @@
             else if (this.field_146045_ax > 0)
             {
                 EntityItem entityitem = new EntityItem(this.worldObj, this.posX, this.posY, this.posZ, this.func_146033_f());
+                // CraftBukkit start
+                PlayerFishEvent playerFishEvent = new PlayerFishEvent((Player) this.field_146042_b.getBukkitEntity(), entityitem.getBukkitEntity(), (Fish) this.getBukkitEntity(), PlayerFishEvent.State.CAUGHT_FISH);
+                playerFishEvent.setExpToDrop(this.rand.nextInt(6) + 1);
+                this.worldObj.getServer().getPluginManager().callEvent(playerFishEvent);
+
+                if (playerFishEvent.isCancelled())
+                {
+                    return 0;
+                }
+
+                // CraftBukkit end
                 double d1 = this.field_146042_b.posX - this.posX;
                 double d3 = this.field_146042_b.posY - this.posY;
                 double d5 = this.field_146042_b.posZ - this.posZ;
@@ -531,15 +561,37 @@
                 entityitem.motionY = d3 * d9 + (double)MathHelper.sqrt_double(d7) * 0.08D;
                 entityitem.motionZ = d5 * d9;
                 this.worldObj.spawnEntityInWorld(entityitem);
-                this.field_146042_b.worldObj.spawnEntityInWorld(new EntityXPOrb(this.field_146042_b.worldObj, this.field_146042_b.posX, this.field_146042_b.posY + 0.5D, this.field_146042_b.posZ + 0.5D, this.rand.nextInt(6) + 1));
+                // CraftBukkit - this.random.nextInt(6) + 1 -> playerFishEvent.getExpToDrop()
+                this.field_146042_b.worldObj.spawnEntityInWorld(new EntityXPOrb(this.field_146042_b.worldObj, this.field_146042_b.posX, this.field_146042_b.posY + 0.5D, this.field_146042_b.posZ + 0.5D, playerFishEvent.getExpToDrop()));
                 b0 = 1;
             }
 
             if (this.field_146051_au)
             {
+                // CraftBukkit start
+                PlayerFishEvent playerFishEvent = new PlayerFishEvent((Player) this.field_146042_b.getBukkitEntity(), null, (Fish) this.getBukkitEntity(), PlayerFishEvent.State.IN_GROUND);
+                this.worldObj.getServer().getPluginManager().callEvent(playerFishEvent);
+
+                if (playerFishEvent.isCancelled())
+                {
+                    return 0;
+                }
+                // CraftBukkit end
                 b0 = 2;
             }
 
+            // CraftBukkit start
+            if (b0 == 0)
+            {
+                PlayerFishEvent playerFishEvent = new PlayerFishEvent((Player) this.field_146042_b.getBukkitEntity(), null, (Fish) this.getBukkitEntity(), PlayerFishEvent.State.FAILED_ATTEMPT);
+                this.worldObj.getServer().getPluginManager().callEvent(playerFishEvent);
+
+                if (playerFishEvent.isCancelled())
+                {
+                    return 0;
+                }
+            }
+            // CraftBukkit end
             this.setDead();
             this.field_146042_b.fishEntity = null;
             return b0;
@@ -580,7 +632,7 @@
             {
                 float f3 = f - f2;
                 this.field_146042_b.addStat(StatList.fishCaughtStat, 1);
-                return ((WeightedRandomFishable)WeightedRandom.getRandomItem(this.rand, field_146036_f)).func_150708_a(this.rand);
+                return ((WeightedRandomFishable) WeightedRandom.getRandomItem(this.rand, EntityFishHook.field_146036_f)).func_150708_a(this.rand); // CraftBukkit - fix static reference to fish list
             }
         }
     }
@@ -594,4 +646,11 @@
             this.field_146042_b.fishEntity = null;
         }
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
