--- ../src-base/minecraft/net/minecraft/entity/projectile/EntityEgg.java
+++ ../src-work/minecraft/net/minecraft/entity/projectile/EntityEgg.java
@@ -6,6 +6,17 @@
 import net.minecraft.util.MovingObjectPosition;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import net.minecraft.entity.player.EntityPlayerMP;
+
+import org.bukkit.entity.Ageable;
+import org.bukkit.entity.EntityType;
+import org.bukkit.entity.Player;
+import org.bukkit.event.player.PlayerEggThrowEvent;
+
+import net.minecraft.entity.Entity;
+// CraftBukkit end
+
 public class EntityEgg extends EntityThrowable
 {
     private static final String __OBFID = "CL_00001724";
@@ -32,24 +43,43 @@
             p_70184_1_.entityHit.attackEntityFrom(DamageSource.causeThrownDamage(this, this.getThrower()), 0.0F);
         }
 
-        if (!this.worldObj.isRemote && this.rand.nextInt(8) == 0)
+        // CraftBukkit start
+        boolean hatching = !this.worldObj.isRemote && this.rand.nextInt(8) == 0;
+        int numHatching = (this.rand.nextInt(32) == 0) ? 4 : 1;
+
+        if (!hatching)
         {
-            byte b0 = 1;
+            numHatching = 0;
+        }
 
-            if (this.rand.nextInt(32) == 0)
-            {
-                b0 = 4;
-            }
+        EntityType hatchingType = EntityType.CHICKEN;
+        Entity shooter = this.getThrower();
 
-            for (int i = 0; i < b0; ++i)
+        if (shooter instanceof EntityPlayerMP)
+        {
+            Player player = (shooter == null) ? null : (Player) shooter.getBukkitEntity();
+            PlayerEggThrowEvent event = new PlayerEggThrowEvent(player, (org.bukkit.entity.Egg) this.getBukkitEntity(), hatching, (byte) numHatching, hatchingType);
+            this.worldObj.getServer().getPluginManager().callEvent(event);
+            hatching = event.isHatching();
+            numHatching = event.getNumHatches();
+            hatchingType = event.getHatchingType();
+        }
+
+        if (hatching)
+        {
+            for (int k = 0; k < numHatching; k++)
             {
-                EntityChicken entitychicken = new EntityChicken(this.worldObj);
-                entitychicken.setGrowingAge(-24000);
-                entitychicken.setLocationAndAngles(this.posX, this.posY, this.posZ, this.rotationYaw, 0.0F);
-                this.worldObj.spawnEntityInWorld(entitychicken);
+                org.bukkit.entity.Entity entity = worldObj.getWorld().spawn(new org.bukkit.Location(worldObj.getWorld(), this.posX, this.posY, this.posZ, this.rotationYaw, 0.0F), hatchingType.getEntityClass(), org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason.EGG);
+
+                if (entity instanceof Ageable)
+                {
+                    ((Ageable) entity).setBaby();
+                }
             }
         }
 
+        // CraftBukkit end
+
         for (int j = 0; j < 8; ++j)
         {
             this.worldObj.spawnParticle("snowballpoof", this.posX, this.posY, this.posZ, 0.0D, 0.0D, 0.0D);
@@ -60,4 +90,11 @@
             this.setDead();
         }
     }
+
+    // Cauldron start
+    @Override
+    public boolean entityProjectileHook() {
+        return true;
+    }
+    // Cauldron end
 }
