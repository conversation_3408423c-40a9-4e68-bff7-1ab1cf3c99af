--- ../src-base/minecraft/net/minecraft/stats/StatFileWriter.java
+++ ../src-work/minecraft/net/minecraft/stats/StatFileWriter.java
@@ -7,6 +7,7 @@
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.util.IJsonSerializable;
 import net.minecraft.util.TupleIntJsonSerializable;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class StatFileWriter
 {
@@ -27,6 +28,12 @@
     {
         if (!p_150871_2_.isAchievement() || this.canUnlockAchievement((Achievement)p_150871_2_))
         {
+            // CraftBukkit start
+            org.bukkit.event.Cancellable cancellable = CraftEventFactory.handleStatisticsIncrease(p_150871_1_, p_150871_2_, this.writeStat(p_150871_2_), p_150871_3_);
+            if (cancellable != null && cancellable.isCancelled()) {
+                return;
+            }
+            // CraftBukkit end
             this.func_150873_a(p_150871_1_, p_150871_2_, this.writeStat(p_150871_2_) + p_150871_3_);
         }
     }
