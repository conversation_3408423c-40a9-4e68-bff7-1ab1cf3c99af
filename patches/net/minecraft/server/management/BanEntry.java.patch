--- ../src-base/minecraft/net/minecraft/server/management/BanEntry.java
+++ ../src-work/minecraft/net/minecraft/server/management/BanEntry.java
@@ -76,4 +76,30 @@
         p_152641_1_.addProperty("expires", this.banEndDate == null ? "forever" : dateFormat.format(this.banEndDate));
         p_152641_1_.addProperty("reason", this.reason);
     }
+
+    // CraftBukkit start
+    public String getSource() {
+        return this.bannedBy;
+    }
+
+    public Date getCreated() {
+        return this.banStartDate;
+    }
+
+    private static Object checkExpiry(Object object, JsonObject jsonobject) {
+        Date expires = null;
+
+        try {
+            expires = jsonobject.has("expires") ? dateFormat.parse(jsonobject.get("expires").getAsString()) : null;
+        } catch (ParseException ex) {
+            // Guess we don't have a date
+        }
+
+        if (expires == null || expires.after(new Date())) {
+            return object;
+        } else {
+            return null;
+        }
+    }
+    // CraftBukkit end
 }
