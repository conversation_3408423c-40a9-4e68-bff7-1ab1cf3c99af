--- ../src-base/minecraft/net/minecraft/server/dedicated/DedicatedServer.java
+++ ../src-work/minecraft/net/minecraft/server/dedicated/DedicatedServer.java
@@ -1,19 +1,21 @@
 package net.minecraft.server.dedicated;
 
+import co.aikar.timings.MinecraftTimings;
 import cpw.mods.fml.common.FMLCommonHandler;
+import cpw.mods.fml.relauncher.FMLRelaunchLog;
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
-import java.io.BufferedReader;
+
 import java.io.File;
 import java.io.IOException;
-import java.io.InputStreamReader;
+import java.lang.reflect.Field;
 import java.net.InetAddress;
 import java.net.Proxy;
-import java.util.ArrayList;
-import java.util.Collections;
-import java.util.List;
-import java.util.Random;
+import java.util.*;
 import java.util.concurrent.Callable;
+
+import io.github.crucible.bootstrap.CrucibleServerMainHook;
+import io.github.crucible.util.CrucibleTracingLoggerOutputStream;
 import net.minecraft.command.ICommandSender;
 import net.minecraft.command.ServerCommand;
 import net.minecraft.crash.CrashReport;
@@ -34,9 +36,22 @@
 import net.minecraft.world.World;
 import net.minecraft.world.WorldSettings;
 import net.minecraft.world.WorldType;
+import net.minecraft.world.chunk.storage.AnvilSaveConverter;
 import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
 
+// CraftBukkit start
+import java.io.PrintStream;
+import java.util.concurrent.ConcurrentMap;
+
+import org.apache.logging.log4j.Level;
+
+import org.apache.logging.log4j.core.LoggerContext;
+import org.bukkit.craftbukkit.v1_7_R4.util.ForwardLogHandler;
+import org.bukkit.craftbukkit.v1_7_R4.util.TerminalConsoleWriterThread;
+import org.bukkit.event.server.ServerCommandEvent;
+// CraftBukkit end
+
 @SideOnly(Side.SERVER)
 public class DedicatedServer extends MinecraftServer implements IServer
 {
@@ -44,7 +59,7 @@
     public final List pendingCommandList = Collections.synchronizedList(new ArrayList());
     private RConThreadQuery theRConThreadQuery;
     private RConThreadMain theRConThreadMain;
-    private PropertyManager settings;
+    public PropertyManager settings; // CraftBukkit - private -> public
     private ServerEula field_154332_n;
     private boolean canSpawnStructures;
     private WorldSettings.GameType gameType;
@@ -52,10 +67,13 @@
     public static boolean allowPlayerLogins = false;
     private static final String __OBFID = "CL_00001784";
 
-    public DedicatedServer(File p_i1508_1_)
+    // CraftBukkit start - Signature changed
+    public DedicatedServer(joptsimple.OptionSet options)
     {
-        super(p_i1508_1_, Proxy.NO_PROXY);
-        Thread thread = new Thread("Server Infinisleeper")
+        super(options, Proxy.NO_PROXY);
+        // super(p_i1508_1_, Proxy.NO_PROXY);
+        // CraftBukkit end
+        new Thread("Server Infinisleeper")
         {
             private static final String __OBFID = "CL_00001787";
             {
@@ -82,31 +100,54 @@
         };
     }
 
-    protected boolean startServer() throws IOException
+    protected boolean startServer() throws java.net.UnknownHostException   // CraftBukkit - throws UnknownHostException
     {
         Thread thread = new Thread("Server console handler")
         {
             private static final String __OBFID = "CL_00001786";
+            final DedicatedServer server = DedicatedServer.this;
             public void run()
             {
-                BufferedReader bufferedreader = new BufferedReader(new InputStreamReader(System.in));
-                String s4;
+                // CraftBukkit start
+                if (!useConsole)
+                {
+                    return;
+                }
+                // CraftBukkit end
 
+                jline.console.ConsoleReader bufferedreader = this.server.reader; // CraftBukkit
+                String s;
+
                 try
                 {
-                    while (!DedicatedServer.this.isServerStopped() && DedicatedServer.this.isServerRunning() && (s4 = bufferedreader.readLine()) != null)
+                    // CraftBukkit start - JLine disabling compatibility
+                    while (!this.server.isServerStopped() && this.server.isServerRunning())
                     {
-                        DedicatedServer.this.addPendingCommand(s4, DedicatedServer.this);
+                        if (useJline)
+                        {
+                            s = bufferedreader.readLine("", null);
+                        }
+                        else
+                        {
+                            s = bufferedreader.readLine();
+                        }
+                        if (s != null)
+                        {
+                            this.server.addPendingCommand(s, this.server);
+                        }
+                        // CraftBukkit end
                     }
+
                 }
-                catch (IOException ioexception1)
+                catch (IOException ioexception)
                 {
-                    DedicatedServer.field_155771_h.error("Exception handling console input", ioexception1);
+                    DedicatedServer.field_155771_h.error("Exception handling console input", ioexception);
                 }
             }
         };
         thread.setDaemon(true);
         thread.start();
+        // CraftBukkit end 
         field_155771_h.info("Starting minecraft server version 1.7.10");
 
         if (Runtime.getRuntime().maxMemory() / 1024L / 1024L < 512L)
@@ -117,7 +158,7 @@
         FMLCommonHandler.instance().onServerStart(this);
 
         field_155771_h.info("Loading properties");
-        this.settings = new PropertyManager(new File("server.properties"));
+        this.settings = new PropertyManager(this.options); // CraftBukkit - CLI argument support
         this.field_154332_n = new ServerEula(new File("eula.txt"));
 
         if (!this.field_154332_n.func_154346_a())
@@ -172,6 +213,18 @@
                 this.setServerPort(this.settings.getIntProperty("server-port", 25565));
             }
 
+            // Spigot start
+            this.func_152361_a((ServerConfigurationManager) (new DedicatedPlayerList(this)));
+            org.spigotmc.SpigotConfig.init();
+            org.spigotmc.SpigotConfig.registerCommands();
+            // Spigot end
+            // Cauldron start
+            //thermosConfig.registerCommands();
+            //cauldronConfig.registerCommands();
+            //tileEntityConfig.registerCommands();
+            //sushchestvoConfig.registerCommands();
+            // Cauldron end
+
             field_155771_h.info("Generating keypair");
             this.setKeyPair(CryptManager.createNewKeyPair());
             field_155771_h.info("Starting Minecraft server on " + (this.getServerHostname().length() == 0 ? "*" : this.getServerHostname()) + ":" + this.getServerPort());
@@ -180,7 +233,7 @@
             {
                 this.func_147137_ag().addLanEndpoint(inetaddress, this.getServerPort());
             }
-            catch (IOException ioexception)
+            catch (Throwable ioexception) // CraftBukkit - IOException -> Throwable
             {
                 field_155771_h.warn("**** FAILED TO BIND TO PORT!");
                 field_155771_h.warn("The exception was: {}", new Object[] {ioexception.toString()});
@@ -196,10 +249,17 @@
                 field_155771_h.warn("To change this, set \"online-mode\" to \"true\" in the server.properties file.");
             }
 
-            if (this.func_152368_aE())
+            try
             {
-                this.func_152358_ax().func_152658_c();
+                if (this.func_152368_aE())
+                {
+                    this.func_152358_ax().func_152658_c();
+                }
             }
+            catch (IOException e)
+            {
+                e.printStackTrace();
+            }
 
             if (!PreYggdrasilConverter.func_152714_a(this.settings))
             {
@@ -208,7 +268,8 @@
             else
             {
                 FMLCommonHandler.instance().onServerStarted();
-                this.func_152361_a(new DedicatedPlayerList(this));
+                // this.func_152361_a(new DedicatedPlayerList(this)); // CraftBukkit - moved up
+                this.anvilConverterForAnvilFile = new AnvilSaveConverter(server.getWorldContainer()); // CraftBukkit - moved from MinecraftServer constructor
                 long j = System.nanoTime();
 
                 if (this.getFolderName() == null)
@@ -274,11 +335,30 @@
                     this.theRConThreadMain.startThread();
                 }
 
+                // CraftBukkit start
+                if (this.server.getBukkitSpawnRadius() > -1)
+                {
+                    field_155771_h
+                            .info("'settings.spawn-radius' in bukkit.yml has been moved to 'spawn-protection' in server.properties. I will move your config for you.");
+                    this.settings.serverProperties.remove("spawn-protection");
+                    this.settings.getIntProperty("spawn-protection", this.server.getBukkitSpawnRadius());
+                    this.server.removeBukkitSpawnRadius();
+                    this.settings.saveProperties();
+                }
+                // CraftBukkit end
+
                 return FMLCommonHandler.instance().handleServerStarting(this);
             }
         }
     }
 
+    // CraftBukkit start
+    public PropertyManager getPropertyManager()
+    {
+        return this.settings;
+    }
+    // CraftBukkit end
+
     public boolean canStructuresSpawn()
     {
         return this.canSpawnStructures;
@@ -364,11 +444,19 @@
 
     public void executePendingCommands()
     {
+        MinecraftTimings.serverCommandTimer.startTiming(); // Spigot
         while (!this.pendingCommandList.isEmpty())
         {
-            ServerCommand servercommand = (ServerCommand)this.pendingCommandList.remove(0);
-            this.getCommandManager().executeCommand(servercommand.sender, servercommand.command);
+            ServerCommand servercommand = (ServerCommand) this.pendingCommandList.remove(0);
+            // CraftBukkit start - ServerCommand for preprocessing
+            ServerCommandEvent event = new ServerCommandEvent(this.console, servercommand.command);
+            this.server.getPluginManager().callEvent(event);
+            servercommand = new ServerCommand(event.getCommand(), servercommand.sender);
+            // this.getCommandManager().executeCommand(servercommand.sender, servercommand.command); // Called in dispatchServerCommand
+            this.server.dispatchServerCommand(this.console, servercommand);
+            // CraftBukkit end
         }
+        MinecraftTimings.serverCommandTimer.stopTiming(); // Spigot
     }
 
     public boolean isDedicatedServer()
