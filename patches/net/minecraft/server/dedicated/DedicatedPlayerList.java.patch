--- ../src-base/minecraft/net/minecraft/server/dedicated/DedicatedPlayerList.java
+++ ../src-work/minecraft/net/minecraft/server/dedicated/DedicatedPlayerList.java
@@ -3,7 +3,11 @@
 import com.mojang.authlib.GameProfile;
 import cpw.mods.fml.relauncher.Side;
 import cpw.mods.fml.relauncher.SideOnly;
+
+import java.io.FileNotFoundException;
 import java.io.IOException;
+
+import io.github.crucible.CrucibleConfigs;
 import net.minecraft.server.MinecraftServer;
 import net.minecraft.server.management.ServerConfigurationManager;
 import org.apache.logging.log4j.LogManager;
@@ -110,6 +114,8 @@
         }
         catch (IOException ioexception)
         {
+            if (ioexception instanceof FileNotFoundException && CrucibleConfigs.configs.crucible_logging_reduceSpam)
+                return;
             field_164439_d.warn("Failed to load ip banlist: ", ioexception);
         }
     }
@@ -122,6 +128,8 @@
         }
         catch (IOException ioexception)
         {
+            if (ioexception instanceof FileNotFoundException && CrucibleConfigs.configs.crucible_logging_reduceSpam)
+                return;
             field_164439_d.warn("Failed to load user banlist: ", ioexception);
         }
     }
@@ -134,6 +142,8 @@
         }
         catch (Exception exception)
         {
+            if (exception instanceof FileNotFoundException && CrucibleConfigs.configs.crucible_logging_reduceSpam)
+                return;
             field_164439_d.warn("Failed to load operators list: ", exception);
         }
     }
@@ -158,6 +168,8 @@
         }
         catch (Exception exception)
         {
+            if (exception instanceof FileNotFoundException && CrucibleConfigs.configs.crucible_logging_reduceSpam)
+                return;
             field_164439_d.warn("Failed to load white-list: ", exception);
         }
     }
