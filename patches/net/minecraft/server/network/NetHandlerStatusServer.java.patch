--- ../src-base/minecraft/net/minecraft/server/network/NetHandlerStatusServer.java
+++ ../src-work/minecraft/net/minecraft/server/network/NetHandlerStatusServer.java
@@ -1,5 +1,7 @@
 package net.minecraft.server.network;
 
+
+import io.netty.channel.ChannelFutureListener;
 import io.netty.util.concurrent.GenericFutureListener;
 import net.minecraft.network.EnumConnectionState;
 import net.minecraft.network.NetworkManager;
@@ -11,12 +13,27 @@
 import net.minecraft.server.MinecraftServer;
 import net.minecraft.util.IChatComponent;
 
+// CraftBukkit start
+import java.net.InetSocketAddress;
+import net.minecraft.network.ServerStatusResponse;
+import net.minecraft.util.ChatComponentText;
+import org.bukkit.craftbukkit.v1_7_R4.CraftServer;
+import org.bukkit.craftbukkit.v1_7_R4.util.CraftIconCache;
+// CraftBukkit end
+
 public class NetHandlerStatusServer implements INetHandlerStatusServer
 {
     private final MinecraftServer field_147314_a;
     private final NetworkManager field_147313_b;
     private static final String __OBFID = "CL_00001464";
 
+    //CraftBukkit start
+    private static final int WAITING = 0;
+    private static final int PING = 1;
+    private static final int DONE = 2;
+    private int state = WAITING;
+    //Craftbukkit end
+
     public NetHandlerStatusServer(MinecraftServer p_i45299_1_, NetworkManager p_i45299_2_)
     {
         this.field_147314_a = p_i45299_1_;
@@ -37,11 +54,55 @@
 
     public void processServerQuery(C00PacketServerQuery p_147312_1_)
     {
-        this.field_147313_b.scheduleOutboundPacket(new S00PacketServerInfo(this.field_147314_a.func_147134_at()), new GenericFutureListener[0]);
+
+        this.field_147313_b.scheduleOutboundPacket(new S00PacketServerInfo(this.field_147314_a.func_147134_at()));
+
+        if (state != WAITING) {
+            this.field_147313_b.closeChannel(null);
+            return;
+        }
+        state = PING;
+        // CraftBukkit start - fire ping event
+        class ServerListPingEvent extends org.bukkit.event.server.ServerListPingEvent
+        {
+            CraftIconCache icon = field_147314_a.server.getServerIcon();
+
+
+            ServerListPingEvent()
+            {
+                super(((InetSocketAddress) field_147313_b.getSocketAddress()).getAddress(), field_147314_a.getMOTD(), field_147314_a.getConfigurationManager().getCurrentPlayerCount(), field_147314_a.getConfigurationManager().getMaxPlayers());
+            }
+
+            @Override
+            public void setServerIcon(org.bukkit.util.CachedServerIcon icon)
+            {
+                if (!(icon instanceof CraftIconCache))
+                {
+                    throw new IllegalArgumentException(icon + " was not created by " + CraftServer.class);
+                }
+
+                this.icon = (CraftIconCache) icon;
+            }
+        }
+        ServerListPingEvent event = new ServerListPingEvent();
+        this.field_147314_a.server.getPluginManager().callEvent(event);
+        ServerStatusResponse ping = new ServerStatusResponse();
+        ping.func_151320_a(event.icon.value);
+        ping.func_151315_a(new ChatComponentText(event.getMotd()));
+        ping.func_151319_a(new ServerStatusResponse.PlayerCountData(event.getMaxPlayers(), field_147314_a.getConfigurationManager().getCurrentPlayerCount()));
+        ping.func_151321_a(new ServerStatusResponse.MinecraftProtocolVersionIdentifier(field_147314_a.getServerModName() + " " + field_147314_a.getMinecraftVersion(), 5)); // TODO: Update when protocol changes
+        this.field_147313_b.scheduleOutboundPacket(new S00PacketServerInfo(ping), new GenericFutureListener[0]);
+        // CraftBukkit end
     }
 
     public void processPing(C01PacketPing p_147311_1_)
     {
-        this.field_147313_b.scheduleOutboundPacket(new S01PacketPong(p_147311_1_.func_149289_c()), new GenericFutureListener[0]);
+        this.field_147313_b.scheduleOutboundPacket(new S01PacketPong(p_147311_1_.func_149289_c()));
+        if (state != PING) {
+            this.field_147313_b.closeChannel(null);
+            return;
+        }
+        state = DONE;
+        this.field_147313_b.scheduleOutboundPacket(new S01PacketPong(p_147311_1_.func_149289_c()), ChannelFutureListener.CLOSE);
     }
 }
