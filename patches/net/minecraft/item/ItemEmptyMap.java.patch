--- ../src-base/minecraft/net/minecraft/item/ItemEmptyMap.java
+++ ../src-work/minecraft/net/minecraft/item/ItemEmptyMap.java
@@ -5,6 +5,7 @@
 import net.minecraft.init.Items;
 import net.minecraft.world.World;
 import net.minecraft.world.storage.MapData;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
 public class ItemEmptyMap extends ItemMapBase
 {
@@ -27,6 +28,7 @@
         mapdata.zCenter = (int)(Math.round(p_77659_3_.posZ / (double)i) * (long)i);
         mapdata.dimension = p_77659_2_.provider.dimensionId;
         mapdata.markDirty();
+        CraftEventFactory.callEvent(new org.bukkit.event.server.MapInitializeEvent(mapdata.mapView)); // CraftBukkit
         --p_77659_1_.stackSize;
 
         if (p_77659_1_.stackSize <= 0)
