--- ../src-base/minecraft/net/minecraft/item/ItemBucket.java
+++ ../src-work/minecraft/net/minecraft/item/ItemBucket.java
@@ -12,6 +12,13 @@
 import net.minecraftforge.common.MinecraftForge;
 import net.minecraftforge.event.entity.player.FillBucketEvent;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftItemStack;
+import org.bukkit.event.player.PlayerBucketEmptyEvent;
+import org.bukkit.event.player.PlayerBucketFillEvent;
+// CraftBukkit end
+
 public class ItemBucket extends Item
 {
     private Block isFull;
@@ -19,7 +26,13 @@
 
     public ItemBucket(Block p_i45331_1_)
     {
-        this.maxStackSize = 1;
+        // PaperSpigot start - Stackable Buckets
+        if ((p_i45331_1_ == Blocks.lava) || (p_i45331_1_ == Blocks.water)) {
+            this.maxStackSize = org.bukkit.Material.BUCKET.getMaxStackSize();
+        } else {
+            this.maxStackSize = 1;
+        }
+        // PaperSpigot end
         this.isFull = p_i45331_1_;
         this.setCreativeTab(CreativeTabs.tabMisc);
     }
@@ -83,23 +96,52 @@
 
                     if (material == Material.water && l == 0)
                     {
+                        // CraftBukkit start
+                        PlayerBucketFillEvent cbEvent = CraftEventFactory.callPlayerBucketFillEvent(p_77659_3_, i, j, k, -1, p_77659_1_, Items.water_bucket);
+
+                        if (cbEvent.isCancelled())
+                        {
+                            return p_77659_1_;
+                        }
+
+                        // CraftBukkit end
                         p_77659_2_.setBlockToAir(i, j, k);
-                        return this.func_150910_a(p_77659_1_, p_77659_3_, Items.water_bucket);
+                        return this.func_150910_a(p_77659_1_, p_77659_3_, Items.water_bucket, cbEvent.getItemStack()); // CraftBukkit - added Event stack
                     }
 
                     if (material == Material.lava && l == 0)
                     {
+                        // CraftBukkit start
+                        PlayerBucketFillEvent cbEvent = CraftEventFactory.callPlayerBucketFillEvent(p_77659_3_, i, j, k, -1, p_77659_1_, Items.lava_bucket);
+
+                        if (cbEvent.isCancelled())
+                        {
+                            return p_77659_1_;
+                        }
+
+                        // CraftBukkit end
                         p_77659_2_.setBlockToAir(i, j, k);
-                        return this.func_150910_a(p_77659_1_, p_77659_3_, Items.lava_bucket);
+                        return this.func_150910_a(p_77659_1_, p_77659_3_, Items.lava_bucket, cbEvent.getItemStack()); // CraftBukkit - added Event stack
                     }
                 }
                 else
                 {
                     if (this.isFull == Blocks.air)
                     {
-                        return new ItemStack(Items.bucket);
+                        // CraftBukkit start
+                        PlayerBucketEmptyEvent cbEvent = CraftEventFactory.callPlayerBucketEmptyEvent(p_77659_3_, i, j, k, movingobjectposition.sideHit, p_77659_1_);
+
+                        if (cbEvent.isCancelled())
+                        {
+                            return p_77659_1_;
+                        }
+
+                        return CraftItemStack.asNMSCopy(cbEvent.getItemStack());
                     }
 
+                    int clickedX = i, clickedY = j, clickedZ = k;
+                    // CraftBukkit end
+
                     if (movingobjectposition.sideHit == 0)
                     {
                         --j;
@@ -135,9 +177,19 @@
                         return p_77659_1_;
                     }
 
+                    // CraftBukkit start
+                    PlayerBucketEmptyEvent cbEvent = CraftEventFactory.callPlayerBucketEmptyEvent(p_77659_3_, clickedX, clickedY, clickedZ, movingobjectposition.sideHit, p_77659_1_);
+
+                    if (cbEvent.isCancelled())
+                    {
+                        return p_77659_1_;
+                    }
+
+                    // CraftBukkit end
+
                     if (this.tryPlaceContainedLiquid(p_77659_2_, i, j, k) && !p_77659_3_.capabilities.isCreativeMode)
                     {
-                        return new ItemStack(Items.bucket);
+                        return CraftItemStack.asNMSCopy(cbEvent.getItemStack()); // CraftBukkit
                     }
                 }
             }
@@ -146,24 +198,32 @@
         }
     }
 
+    // Cauldron start - vanilla compatibility
     private ItemStack func_150910_a(ItemStack p_150910_1_, EntityPlayer p_150910_2_, Item p_150910_3_)
     {
-        if (p_150910_2_.capabilities.isCreativeMode)
+        return this.func_150910_a(p_150910_1_, p_150910_2_, p_150910_3_, null);
+    }
+    // Cauldron end
+
+    // CraftBukkit - added ob.ItemStack result - TODO: Is this... the right way to handle this?
+    private ItemStack func_150910_a(ItemStack itemstack, EntityPlayer entityplayer, Item item, org.bukkit.inventory.ItemStack result)
+    {
+        if (entityplayer.capabilities.isCreativeMode)
         {
-            return p_150910_1_;
+            return itemstack;
         }
-        else if (--p_150910_1_.stackSize <= 0)
+        else if (--itemstack.stackSize <= 0)
         {
-            return new ItemStack(p_150910_3_);
+            return CraftItemStack.asNMSCopy(result); // CraftBukkit
         }
         else
         {
-            if (!p_150910_2_.inventory.addItemStackToInventory(new ItemStack(p_150910_3_)))
+            if (!entityplayer.inventory.addItemStackToInventory(CraftItemStack.asNMSCopy(result)))   // CraftBukkit
             {
-                p_150910_2_.dropPlayerItemWithRandomChoice(new ItemStack(p_150910_3_, 1, 0), false);
+                entityplayer.dropPlayerItemWithRandomChoice(CraftItemStack.asNMSCopy(result), false); // CraftBukkit
             }
 
-            return p_150910_1_;
+            return itemstack;
         }
     }
 
