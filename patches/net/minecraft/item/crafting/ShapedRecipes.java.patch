--- ../src-base/minecraft/net/minecraft/item/crafting/ShapedRecipes.java
+++ ../src-work/minecraft/net/minecraft/item/crafting/ShapedRecipes.java
@@ -5,6 +5,12 @@
 import net.minecraft.nbt.NBTTagCompound;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftItemStack;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftShapedRecipe;
+import org.bukkit.craftbukkit.v1_7_R4.util.CraftMagicNumbers;
+// CraftBukkit end
+
 public class ShapedRecipes implements IRecipe
 {
     public final int recipeWidth;
@@ -22,6 +28,77 @@
         this.recipeOutput = p_i1917_4_;
     }
 
+    // CraftBukkit start
+    public org.bukkit.inventory.ShapedRecipe toBukkitRecipe()
+    {
+        CraftItemStack result = CraftItemStack.asCraftMirror(this.recipeOutput);
+        CraftShapedRecipe recipe = new CraftShapedRecipe(result, this);
+
+        switch (this.recipeHeight)
+        {
+            case 1:
+                switch (this.recipeWidth)
+                {
+                    case 1:
+                        recipe.shape("a");
+                        break;
+                    case 2:
+                        recipe.shape("ab");
+                        break;
+                    case 3:
+                        recipe.shape("abc");
+                        break;
+                }
+
+                break;
+            case 2:
+                switch (this.recipeWidth)
+                {
+                    case 1:
+                        recipe.shape("a", "b");
+                        break;
+                    case 2:
+                        recipe.shape("ab", "cd");
+                        break;
+                    case 3:
+                        recipe.shape("abc", "def");
+                        break;
+                }
+
+                break;
+            case 3:
+                switch (this.recipeWidth)
+                {
+                    case 1:
+                        recipe.shape("a", "b", "c");
+                        break;
+                    case 2:
+                        recipe.shape("ab", "cd", "ef");
+                        break;
+                    case 3:
+                        recipe.shape("abc", "def", "ghi");
+                        break;
+                }
+
+                break;
+        }
+
+        char c = 'a';
+
+        for (ItemStack stack : this.recipeItems)
+        {
+            if (stack != null)
+            {
+                recipe.setIngredient(c, CraftMagicNumbers.getMaterial(stack.getItem()), stack.getItemDamage());
+            }
+
+            c++;
+        }
+
+        return recipe;
+    }
+    // CraftBukkit end
+
     public ItemStack getRecipeOutput()
     {
         return this.recipeOutput;
