--- ../src-base/minecraft/net/minecraft/item/crafting/FurnaceRecipes.java
+++ ../src-work/minecraft/net/minecraft/item/crafting/FurnaceRecipes.java
@@ -2,6 +2,7 @@
 
 import java.util.HashMap;
 import java.util.Iterator;
+import java.util.LinkedHashMap;
 import java.util.Map;
 import java.util.Map.Entry;
 import net.minecraft.block.Block;
@@ -14,8 +15,9 @@
 public class FurnaceRecipes
 {
     private static final FurnaceRecipes smeltingBase = new FurnaceRecipes();
-    private Map smeltingList = new HashMap();
-    private Map experienceList = new HashMap();
+    public Map smeltingList = new LinkedHashMap(); // CraftBukkit - private -> public
+    private Map experienceList = new LinkedHashMap();
+    public Map customRecipes = new LinkedHashMap(); // CraftBukkit
     private static final String __OBFID = "CL_00000085";
 
     public static FurnaceRecipes smelting()
@@ -23,7 +25,7 @@
         return smeltingBase;
     }
 
-    private FurnaceRecipes()
+    public FurnaceRecipes()   // CraftBukkit - private -> public
     {
         this.func_151393_a(Blocks.iron_ore, new ItemStack(Items.iron_ingot), 0.7F);
         this.func_151393_a(Blocks.gold_ore, new ItemStack(Items.gold_ingot), 1.0F);
@@ -76,16 +78,37 @@
         this.experienceList.put(p_151394_2_, Float.valueOf(p_151394_3_));
     }
 
+    // CraftBukkit start
+    public void registerRecipe(ItemStack itemstack, ItemStack itemstack1)
+    {
+        this.customRecipes.put(itemstack, itemstack1);
+    }
+    // CraftBukkit end
+
     public ItemStack getSmeltingResult(ItemStack p_151395_1_)
     {
-        Iterator iterator = this.smeltingList.entrySet().iterator();
+        // CraftBukkit start
+        boolean vanilla = false;
+        Iterator iterator = this.customRecipes.entrySet().iterator();
+        // CraftBukkit end
         Entry entry;
 
         do
         {
             if (!iterator.hasNext())
             {
-                return null;
+                // CraftBukkit start
+                if (!vanilla)
+                {
+                    iterator = this.smeltingList.entrySet().iterator();
+                    vanilla = true;
+                }
+                else
+                {
+                    return null;
+                }
+
+                // CraftBukkit end
             }
 
             entry = (Entry)iterator.next();
