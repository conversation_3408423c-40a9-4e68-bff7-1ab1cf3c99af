--- ../src-base/minecraft/net/minecraft/item/crafting/RecipesArmorDyes.java
+++ ../src-work/minecraft/net/minecraft/item/crafting/RecipesArmorDyes.java
@@ -9,10 +9,17 @@
 import net.minecraft.item.ItemStack;
 import net.minecraft.world.World;
 
-public class RecipesArmorDyes implements IRecipe
+public class RecipesArmorDyes extends ShapelessRecipes implements IRecipe   // CraftBukkit - added extends
 {
     private static final String __OBFID = "CL_00000079";
 
+    // CraftBukkit start - Delegate to new parent class with bogus info
+    public RecipesArmorDyes()
+    {
+        super(new ItemStack(Items.leather_helmet, 0, 0), java.util.Arrays.asList(new ItemStack(Items.dye, 0, 5)));
+    }
+    // CraftBukkit end
+
     public boolean matches(InventoryCrafting p_77569_1_, World p_77569_2_)
     {
         ItemStack itemstack = null;
