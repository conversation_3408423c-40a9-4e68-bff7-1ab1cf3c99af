--- ../src-base/minecraft/net/minecraft/item/crafting/RecipesMapCloning.java
+++ ../src-work/minecraft/net/minecraft/item/crafting/RecipesMapCloning.java
@@ -5,10 +5,17 @@
 import net.minecraft.item.ItemStack;
 import net.minecraft.world.World;
 
-public class RecipesMapCloning implements IRecipe
+public class RecipesMapCloning extends ShapelessRecipes implements IRecipe   // CraftBukkit - added extends
 {
     private static final String __OBFID = "CL_00000087";
 
+    // CraftBukkit start - Delegate to new parent class
+    public RecipesMapCloning()
+    {
+        super(new ItemStack(Items.filled_map, 0, -1), java.util.Arrays.asList(new ItemStack(Items.map, 0, 0)));
+    }
+    // CraftBukkit end
+
     public boolean matches(InventoryCrafting p_77569_1_, World p_77569_2_)
     {
         int i = 0;
