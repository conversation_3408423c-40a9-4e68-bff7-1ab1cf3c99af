--- ../src-base/minecraft/net/minecraft/item/crafting/ShapelessRecipes.java
+++ ../src-work/minecraft/net/minecraft/item/crafting/ShapelessRecipes.java
@@ -7,6 +7,12 @@
 import net.minecraft.item.ItemStack;
 import net.minecraft.world.World;
 
+// CraftBukkit start
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftItemStack;
+import org.bukkit.craftbukkit.v1_7_R4.inventory.CraftShapelessRecipe;
+import org.bukkit.craftbukkit.v1_7_R4.util.CraftMagicNumbers;
+// CraftBukkit end
+
 public class ShapelessRecipes implements IRecipe
 {
     private final ItemStack recipeOutput;
@@ -19,6 +25,25 @@
         this.recipeItems = p_i1918_2_;
     }
 
+    // CraftBukkit start
+    @SuppressWarnings("unchecked")
+    public org.bukkit.inventory.ShapelessRecipe toBukkitRecipe()
+    {
+        CraftItemStack result = CraftItemStack.asCraftMirror(this.recipeOutput);
+        CraftShapelessRecipe recipe = new CraftShapelessRecipe(result, this);
+
+        for (ItemStack stack : (List<ItemStack>) this.recipeItems)
+        {
+            if (stack != null)
+            {
+                recipe.addIngredient(CraftMagicNumbers.getMaterial(stack.getItem()), stack.getItemDamage());
+            }
+        }
+
+        return recipe;
+    }
+    // CraftBukkit end
+
     public ItemStack getRecipeOutput()
     {
         return this.recipeOutput;
