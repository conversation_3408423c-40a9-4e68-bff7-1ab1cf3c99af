--- ../src-base/minecraft/net/minecraft/crash/CrashReport.java
+++ ../src-work/minecraft/net/minecraft/crash/CrashReport.java
@@ -15,12 +15,17 @@
 import java.util.Iterator;
 import java.util.List;
 import java.util.concurrent.Callable;
+
+import io.github.crucible.bootstrap.CrucibleMetadata;
 import net.minecraft.util.ReportedException;
 import net.minecraft.world.gen.layer.IntCache;
+
 import org.apache.commons.io.IOUtils;
 import org.apache.commons.lang3.ArrayUtils;
 import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
+import org.bukkit.Bukkit;
+import org.bukkit.plugin.Plugin;
 
 public class CrashReport
 {
@@ -51,6 +56,38 @@
                 return "1.7.10";
             }
         });
+        this.theReportCategory.addCrashSectionCallable("Crucible Version", new Callable<String>() {
+            @Override
+            public String call() throws Exception {
+                return String.format("%s", CrucibleMetadata.CRUCIBLE_VERSION);
+            }
+        });
+        this.theReportCategory.addCrashSectionCallable("Plugins", new Callable<String>() {
+            @Override
+            public String call() throws Exception {
+                StringBuilder pluginList = new StringBuilder();
+                Plugin[] plugins = Bukkit.getPluginManager().getPlugins();
+                for (Plugin plugin : plugins) {
+                    if (pluginList.length() > 0)
+                        pluginList.append(", ");
+                    if (plugin.isEnabled()) pluginList.append(plugin.getDescription().getName());
+                }
+                return pluginList.toString();
+            }
+        });
+        this.theReportCategory.addCrashSectionCallable("Disabled Plugins", new Callable<String>() {
+            @Override
+            public String call() throws Exception {
+                StringBuilder pluginList = new StringBuilder();
+                Plugin[] plugins = Bukkit.getPluginManager().getPlugins();
+                for (Plugin plugin : plugins) {
+                    if (pluginList.length() > 0)
+                        pluginList.append(", ");
+                    if (!plugin.isEnabled()) pluginList.append(plugin.getDescription().getName());
+                }
+                return pluginList.toString();
+            }
+        });
         this.theReportCategory.addCrashSectionCallable("Operating System", new Callable()
         {
             private static final String __OBFID = "CL_00001222";
