--- ../src-base/minecraft/net/minecraft/world/gen/structure/MapGenStructure.java
+++ ../src-work/minecraft/net/minecraft/world/gen/structure/MapGenStructure.java
@@ -239,8 +239,17 @@
     {
         if (this.field_143029_e == null)
         {
-            this.field_143029_e = (MapGenStructureData)p_143027_1_.perWorldStorage.loadData(MapGenStructureData.class, this.func_143025_a());
+            // Spigot Start
+            if (p_143027_1_.getSpigotConfig().saveStructureInfo && !this.func_143025_a().equals("Mineshaft")) // Cauldron
+            {
+                this.field_143029_e = (MapGenStructureData) p_143027_1_.loadItemData(MapGenStructureData.class, this.func_143025_a());
+            }
+            else
+            {
+                this.field_143029_e = new MapGenStructureData(this.func_143025_a());
+            }
 
+            // Spigot End
             if (this.field_143029_e == null)
             {
                 this.field_143029_e = new MapGenStructureData(this.func_143025_a());
