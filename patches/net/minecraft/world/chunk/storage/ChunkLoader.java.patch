--- ../src-base/minecraft/net/minecraft/world/chunk/storage/ChunkLoader.java
+++ ../src-work/minecraft/net/minecraft/world/chunk/storage/ChunkLoader.java
@@ -113,9 +113,11 @@
                 NBTTagCompound nbttagcompound1 = new NBTTagCompound();
                 nbttagcompound1.setByte("Y", (byte)(j & 255));
                 nbttagcompound1.setByteArray("Blocks", abyte1);
-                nbttagcompound1.setByteArray("Data", nibblearray.data);
-                nbttagcompound1.setByteArray("SkyLight", nibblearray1.data);
-                nbttagcompound1.setByteArray("BlockLight", nibblearray2.data);
+                // Spigot start - data -> getValueArray() accessor
+                nbttagcompound1.setByteArray("Data", nibblearray.getValueArray());
+                nbttagcompound1.setByteArray("SkyLight", nibblearray1.getValueArray());
+                nbttagcompound1.setByteArray("BlockLight", nibblearray2.getValueArray());
+                // Spigot end
                 nbttaglist.appendTag(nbttagcompound1);
             }
         }
