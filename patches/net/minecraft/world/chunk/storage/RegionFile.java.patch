--- ../src-base/minecraft/net/minecraft/world/chunk/storage/RegionFile.java
+++ ../src-work/minecraft/net/minecraft/world/chunk/storage/RegionFile.java
@@ -1,373 +1,323 @@
 package net.minecraft.world.chunk.storage;
 
-import java.io.BufferedInputStream;
-import java.io.ByteArrayInputStream;
-import java.io.ByteArrayOutputStream;
-import java.io.DataInputStream;
-import java.io.DataOutputStream;
-import java.io.File;
-import java.io.IOException;
-import java.io.RandomAccessFile;
+import cpw.mods.fml.common.FMLLog;
+import io.github.crucible.CrucibleConfigs;
+import io.github.crucible.CrucibleModContainer;
+import net.minecraft.server.MinecraftServer;
+
+import java.io.*;
 import java.util.ArrayList;
+import java.util.List;
+import java.util.regex.Pattern;
 import java.util.zip.DeflaterOutputStream;
 import java.util.zip.GZIPInputStream;
 import java.util.zip.InflaterInputStream;
-import net.minecraft.server.MinecraftServer;
 
-public class RegionFile
-{
-    private static final byte[] emptySector = new byte[4096];
+public class RegionFile {
+    // Minecraft is limited to 256 sections per chunk. So 1MB. This can easily be override.
+    // So we extend this to use the REAL size when the count is maxed by seeking to that section and reading the length.
+    private static final boolean FORGE_ENABLE_EXTENDED_SAVE = CrucibleConfigs.configs.crucible_enableOversizedChunk;
+    private static final boolean FORGE_ENABLE_EXTENDED_WARNING = CrucibleConfigs.configs.crucible_warnOversizedChunk;
+    private static final long SECTOR_LENGTH = 4096L;
+    private static final byte[] EMPTY_SECTOR = new byte[(int) SECTOR_LENGTH];
     private final File fileName;
-    private RandomAccessFile dataFile;
     private final int[] offsets = new int[1024];
     private final int[] chunkTimestamps = new int[1024];
-    private ArrayList sectorFree;
+    private List<Boolean> sectorFree;
     private int sizeDelta;
     private long lastModified;
     private static final String __OBFID = "CL_00000381";
+    private String filePathName = null;
 
-    public RegionFile(File p_i2001_1_)
-    {
-        this.fileName = p_i2001_1_;
+    private RandomAccessFile dataFile = null;
+
+    public RegionFile(File fileNameIn) {
+        this.fileName = fileNameIn;
         this.sizeDelta = 0;
 
-        try
-        {
-            if (p_i2001_1_.exists())
-            {
-                this.lastModified = p_i2001_1_.lastModified();
+        try {
+            if (fileNameIn.exists()) {
+                this.lastModified = fileNameIn.lastModified();
             }
 
-            this.dataFile = new RandomAccessFile(p_i2001_1_, "rw");
-            int i;
+            RandomAccessFile dataFile = new RandomAccessFile(fileNameIn, "rw");
 
-            if (this.dataFile.length() < 4096L)
-            {
-                for (i = 0; i < 1024; ++i)
-                {
-                    this.dataFile.writeInt(0);
-                }
+            this.dataFile = dataFile;
+            //int i;
+            if (this.dataFile.length() < SECTOR_LENGTH) {
+                // Spigot - more efficient chunk zero'ing
+                this.dataFile.write(RegionFile.EMPTY_SECTOR); // Spigot // Crucible - info:this sector is the chunk offset table
+                this.dataFile.write(RegionFile.EMPTY_SECTOR); // Spigot // Crucible - info:this sector is the timestamp info
 
-                for (i = 0; i < 1024; ++i)
-                {
-                    this.dataFile.writeInt(0);
-                }
-
-                this.sizeDelta += 8192;
+                this.sizeDelta += SECTOR_LENGTH * 2;
             }
 
-            if ((this.dataFile.length() & 4095L) != 0L)
-            {
-                for (i = 0; (long)i < (this.dataFile.length() & 4095L); ++i)
-                {
+            if ((this.dataFile.length() & 4095L) != 0L) {
+                for (int i = 0; (long) i < (this.dataFile.length() & 4095L); ++i) {
                     this.dataFile.write(0);
                 }
             }
 
-            i = (int)this.dataFile.length() / 4096;
-            this.sectorFree = new ArrayList(i);
-            int j;
+            int freeSectors = (int) this.dataFile.length() / 4096;
+            this.sectorFree = new ArrayList<>(freeSectors);
+            //int j;
 
-            for (j = 0; j < i; ++j)
-            {
-                this.sectorFree.add(Boolean.valueOf(true));
+            for (int i = 0; i < freeSectors; ++i) {
+                this.sectorFree.add(true);
             }
 
-            this.sectorFree.set(0, Boolean.valueOf(false));
-            this.sectorFree.set(1, Boolean.valueOf(false));
+            //Sections already used by the offset table and timestamp
+            this.sectorFree.set(0, false);
+            this.sectorFree.set(1, false);
+
             this.dataFile.seek(0L);
-            int k;
+            for (int i = 0; i < 1024; ++i) {
 
-            for (j = 0; j < 1024; ++j)
-            {
-                k = this.dataFile.readInt();
-                this.offsets[j] = k;
+                int offset = this.dataFile.readInt();
+                this.offsets[i] = offset;
+                // Spigot start
+                int length = offset & 255;
+                if (length == 255) {
 
-                if (k != 0 && (k >> 8) + (k & 255) <= this.sectorFree.size())
-                {
-                    for (int l = 0; l < (k & 255); ++l)
-                    {
-                        this.sectorFree.set((k >> 8) + l, Boolean.valueOf(false));
+                    // We're maxed out, so we need to read the proper length from the section
+                    if ((offset >> 8) <= this.sectorFree.size()) {
+                        this.dataFile.seek((offset >> 8) * 4096L);
+                        length = (this.dataFile.readInt() + 4) / 4096 + 1;
+                        this.dataFile.seek(i * 4 + 4); //Go back to where we were
                     }
                 }
+                if (offset != 0 && (offset >> 8) + length <= this.sectorFree.size()) {
+                    for (int l = 0; l < length; ++l) {
+                        // Spigot end
+                        this.sectorFree.set((offset >> 8) + l, false);
+                    }
+                } else if (length > 0)
+                    FMLLog.warning("Invalid chunk: (%s, %s) Offset: %s Length: %s runs off end file. %s", i % 32, i / 32, offset >> 8, length, fileNameIn);
             }
-
-            for (j = 0; j < 1024; ++j)
-            {
-                k = this.dataFile.readInt();
-                this.chunkTimestamps[j] = k;
+            for (int i = 0; i < 1024; ++i) {
+                int timestamp = this.dataFile.readInt();
+                this.chunkTimestamps[i] = timestamp;
             }
-        }
-        catch (IOException ioexception)
-        {
+
+
+        } catch (IOException ioexception) {
             ioexception.printStackTrace();
         }
     }
 
     // This is a copy (sort of) of the method below it, make sure they stay in sync
-    public synchronized boolean chunkExists(int x, int z)
-    {
-        if (this.outOfBounds(x, z)) return false;
-
-        try
-        {
-            int offset = this.getOffset(x, z);
-
-            if (offset == 0) return false;
-
-            int sectorNumber = offset >> 8;
-            int numSectors = offset & 255;
-
-            if (sectorNumber + numSectors > this.sectorFree.size()) return false;
-
-            this.dataFile.seek((long)(sectorNumber * 4096));
-            int length = this.dataFile.readInt();
-
-            if (length > 4096 * numSectors || length <= 0) return false;
-
-            byte version = this.dataFile.readByte();
-
-            if (version == 1 || version == 2) return true;
-        }
-        catch (IOException ioexception)
-        {
-            return false;
-        }
-
-        return false;
+    public synchronized boolean chunkExists(int x, int z) {
+        return isChunkSaved(x, z);
     }
 
-    public synchronized DataInputStream getChunkDataInputStream(int p_76704_1_, int p_76704_2_)
-    {
-        if (this.outOfBounds(p_76704_1_, p_76704_2_))
-        {
+    public synchronized DataInputStream getChunkDataInputStream(int x, int z) {
+        if (this.outOfBounds(x, z)) {
             return null;
-        }
-        else
-        {
-            try
-            {
-                int k = this.getOffset(p_76704_1_, p_76704_2_);
+        } else {
+            try {
+                int offset = this.getOffset(x, z);
 
-                if (k == 0)
-                {
+                if (offset == 0) {
                     return null;
-                }
-                else
-                {
-                    int l = k >> 8;
-                    int i1 = k & 255;
-
-                    if (l + i1 > this.sectorFree.size())
-                    {
+                } else {
+                    int sector = offset >> 8;
+                    int sectorCount = offset & 255;
+                    // Spigot start
+                    if (sectorCount == 255) {
+                        this.dataFile.seek(sector * SECTOR_LENGTH);
+                        sectorCount = (this.dataFile.readInt() + 4) / 4096 + 1;
+                    } else {
+					}
+                    // Spigot end
+                    if (sector + sectorCount > this.sectorFree.size()) {
                         return null;
-                    }
-                    else
-                    {
-                        this.dataFile.seek((long)(l * 4096));
-                        int j1 = this.dataFile.readInt();
+                    } else {
+                        this.dataFile.seek(sector * 4096L);
+                        int length = this.dataFile.readInt();
 
-                        if (j1 > 4096 * i1)
-                        {
+                        if (length > 4096 * sectorCount) {
                             return null;
-                        }
-                        else if (j1 <= 0)
-                        {
+                        } else if (length <= 0) {
                             return null;
-                        }
-                        else
-                        {
-                            byte b0 = this.dataFile.readByte();
-                            byte[] abyte;
+                        } else {
+                            byte compressionType = this.dataFile.readByte();
+                            byte[] compressedData;
 
-                            if (b0 == 1)
-                            {
-                                abyte = new byte[j1 - 1];
-                                this.dataFile.read(abyte);
-                                return new DataInputStream(new BufferedInputStream(new GZIPInputStream(new ByteArrayInputStream(abyte))));
-                            }
-                            else if (b0 == 2)
-                            {
-                                abyte = new byte[j1 - 1];
-                                this.dataFile.read(abyte);
-                                return new DataInputStream(new BufferedInputStream(new InflaterInputStream(new ByteArrayInputStream(abyte))));
-                            }
-                            else
-                            {
+                            if (compressionType == 1) {
+                                compressedData = new byte[length - 1];
+                                this.dataFile.read(compressedData);
+                                return new DataInputStream(new BufferedInputStream(new GZIPInputStream(new ByteArrayInputStream(compressedData))));
+                            } else if (compressionType == 2) {
+                                compressedData = new byte[length - 1];
+                                this.dataFile.read(compressedData);
+                                return new DataInputStream(new BufferedInputStream(new InflaterInputStream(new ByteArrayInputStream(compressedData))));
+                            } else {
                                 return null;
                             }
                         }
                     }
                 }
-            }
-            catch (IOException ioexception)
-            {
+            } catch (IOException ioexception) {
+            	ioexception.printStackTrace();
                 return null;
             }
         }
     }
 
-    public DataOutputStream getChunkDataOutputStream(int p_76710_1_, int p_76710_2_)
-    {
-        return this.outOfBounds(p_76710_1_, p_76710_2_) ? null : new DataOutputStream(new DeflaterOutputStream(new RegionFile.ChunkBuffer(p_76710_1_, p_76710_2_)));
+    public DataOutputStream getChunkDataOutputStream(int x, int z) {
+        return this.outOfBounds(x, z) ? null : new DataOutputStream(new java.io.BufferedOutputStream(new DeflaterOutputStream(new RegionFile.ChunkBuffer(x, z)))); // Spigot - use a BufferedOutputStream to greatly improve file write performance
     }
 
-    protected synchronized void write(int p_76706_1_, int p_76706_2_, byte[] p_76706_3_, int p_76706_4_)
-    {
-        try
-        {
-            int l = this.getOffset(p_76706_1_, p_76706_2_);
-            int i1 = l >> 8;
-            int j1 = l & 255;
-            int k1 = (p_76706_4_ + 5) / 4096 + 1;
+    public String getFilePathName() {
+        if (filePathName == null){
+            String[] parts = this.fileName.getAbsolutePath().split(Pattern.quote("\\"));
+            StringBuilder stringBuilder = new StringBuilder();
+            for (int i = Math.max(0, parts.length - 3); i < parts.length; i++) {
+                stringBuilder.append(parts[i]);
+                if (i < parts.length - 1) {
+                    stringBuilder.append("/");
+                }
+            }
+            filePathName = stringBuilder.toString();
+        }
+        return filePathName;
+    }
 
-            if (k1 >= 256)
-            {
-                return;
+    protected synchronized void write(int x, int z, byte[] data, int length) {
+        try {
+            int offset = this.getOffset(x, z);
+            int sector = offset >> 8;
+            int sectorCount = offset & 255;
+            // Spigot start
+            if (sectorCount == 255) {
+                this.dataFile.seek(sector * SECTOR_LENGTH);
+                sectorCount = (this.dataFile.readInt() + 4) / 4096 + 1;
             }
+            // Spigot end
+            int sectorsNeeded = (length + 5) / 4096 + 1;
 
-            if (i1 != 0 && j1 == k1)
-            {
-                this.write(i1, p_76706_3_, p_76706_4_);
+            if (sectorsNeeded >= 256) { //crucible - info: chunk has a limit of 255 sectors
+                if (FORGE_ENABLE_EXTENDED_WARNING){
+                    CrucibleModContainer.logger.warn("[Crucible] Found Oversized Chunk to be saved at [{}] in Chunk ({}, {})! Its recommended to not overload a chunk with too much data!", getFilePathName(), x, z);
+                }
+                if (!FORGE_ENABLE_EXTENDED_SAVE) {
+                    return;
+                }
             }
-            else
-            {
-                int l1;
 
-                for (l1 = 0; l1 < j1; ++l1)
-                {
-                    this.sectorFree.set(i1 + l1, Boolean.valueOf(true));
+            if (sector != 0 && sectorCount == sectorsNeeded) {
+            	//crucible - info: this part just overwrite the current old sectors.
+                this.write(sector, data, length);
+            } else {
+
+                for (int i = 0; i < sectorCount; ++i) {
+                    this.sectorFree.set(sector + i, true);
                 }
 
-                l1 = this.sectorFree.indexOf(Boolean.valueOf(true));
-                int i2 = 0;
-                int j2;
+                int sectorStart = this.sectorFree.indexOf(true);
+                int sectorLength = 0;
 
-                if (l1 != -1)
-                {
-                    for (j2 = l1; j2 < this.sectorFree.size(); ++j2)
-                    {
-                        if (i2 != 0)
-                        {
-                            if (((Boolean)this.sectorFree.get(j2)).booleanValue())
-                            {
-                                ++i2;
+				//crucible - info: search for an area with enough free space
+                if (sectorStart != -1) {
+                    for (int i = sectorStart; i < this.sectorFree.size(); ++i) {
+                        if (sectorLength != 0) {
+                            if (this.sectorFree.get(i)) {
+                                ++sectorLength;
+                            } else {
+                                sectorLength = 0;
                             }
-                            else
-                            {
-                                i2 = 0;
-                            }
+                        } else if (this.sectorFree.get(i)) {
+                            sectorStart = i;
+                            sectorLength = 1;
                         }
-                        else if (((Boolean)this.sectorFree.get(j2)).booleanValue())
-                        {
-                            l1 = j2;
-                            i2 = 1;
-                        }
 
-                        if (i2 >= k1)
-                        {
+                        if (sectorLength >= sectorsNeeded) {
                             break;
                         }
                     }
                 }
 
-                if (i2 >= k1)
-                {
-                    i1 = l1;
-                    this.setOffset(p_76706_1_, p_76706_2_, l1 << 8 | k1);
+                if (sectorLength >= sectorsNeeded) {
+					//crucible - info: space found.
+                    sector = sectorStart;
+                    this.setOffset(x, z, sector << 8 | (sectorsNeeded > 255 ? 255 : sectorsNeeded)); // Spigot
 
-                    for (j2 = 0; j2 < k1; ++j2)
-                    {
-                        this.sectorFree.set(i1 + j2, Boolean.valueOf(false));
+                    for (int i = 0; i < sectorsNeeded; ++i) {
+                        this.sectorFree.set(sector + i, false);
                     }
 
-                    this.write(i1, p_76706_3_, p_76706_4_);
-                }
-                else
-                {
+                    this.write(sector, data, length);
+                } else {
+					//crucible - info: space nof found, grow the file.
                     this.dataFile.seek(this.dataFile.length());
-                    i1 = this.sectorFree.size();
+                    sector = this.sectorFree.size();
 
-                    for (j2 = 0; j2 < k1; ++j2)
-                    {
-                        this.dataFile.write(emptySector);
-                        this.sectorFree.add(Boolean.valueOf(false));
+                    for (int i = 0; i < sectorsNeeded; ++i) {
+                        this.dataFile.write(EMPTY_SECTOR);
+                        this.sectorFree.add(false);
                     }
 
-                    this.sizeDelta += 4096 * k1;
-                    this.write(i1, p_76706_3_, p_76706_4_);
-                    this.setOffset(p_76706_1_, p_76706_2_, i1 << 8 | k1);
+                    this.sizeDelta += 4096 * sectorsNeeded;
+                    this.write(sector, data, length);
+                    this.setOffset(x, z, sector << 8 | (sectorsNeeded > 255 ? 255 : sectorsNeeded)); // Spigot
                 }
             }
 
-            this.setChunkTimestamp(p_76706_1_, p_76706_2_, (int)(MinecraftServer.getSystemTimeMillis() / 1000L));
-        }
-        catch (IOException ioexception)
-        {
+            this.setChunkTimestamp(x, z, (int) (MinecraftServer.getSystemTimeMillis() / 1000L));
+        } catch (IOException ioexception) {
             ioexception.printStackTrace();
         }
     }
 
-    private void write(int p_76712_1_, byte[] p_76712_2_, int p_76712_3_) throws IOException
-    {
-        this.dataFile.seek((long)(p_76712_1_ * 4096));
-        this.dataFile.writeInt(p_76712_3_ + 1);
+    private void write(int sectorNumber, byte[] data, int length) throws IOException {
+        this.dataFile.seek(sectorNumber * SECTOR_LENGTH);
+        this.dataFile.writeInt(length + 1);
         this.dataFile.writeByte(2);
-        this.dataFile.write(p_76712_2_, 0, p_76712_3_);
+        this.dataFile.write(data, 0, length);
     }
 
-    private boolean outOfBounds(int p_76705_1_, int p_76705_2_)
-    {
-        return p_76705_1_ < 0 || p_76705_1_ >= 32 || p_76705_2_ < 0 || p_76705_2_ >= 32;
+    public boolean outOfBounds(int x, int z) {
+        return x < 0 || x >= 32 || z < 0 || z >= 32;
     }
 
-    private int getOffset(int p_76707_1_, int p_76707_2_)
-    {
-        return this.offsets[p_76707_1_ + p_76707_2_ * 32];
+    public int getOffset(int x, int z) {
+        return this.offsets[x + z * 32];
     }
 
-    public boolean isChunkSaved(int p_76709_1_, int p_76709_2_)
-    {
-        return this.getOffset(p_76709_1_, p_76709_2_) != 0;
+    public boolean isChunkSaved(int x, int z) {
+        return this.getOffset(x, z) != 0;
     }
 
-    private void setOffset(int p_76711_1_, int p_76711_2_, int p_76711_3_) throws IOException
-    {
-        this.offsets[p_76711_1_ + p_76711_2_ * 32] = p_76711_3_;
-        this.dataFile.seek((long)((p_76711_1_ + p_76711_2_ * 32) * 4));
-        this.dataFile.writeInt(p_76711_3_);
+    private void setOffset(int x, int z, int offset) throws IOException {
+        this.offsets[x + z * 32] = offset;
+        this.dataFile.seek((x + z * 32) * 4);
+        this.dataFile.writeInt(offset);
     }
 
-    private void setChunkTimestamp(int p_76713_1_, int p_76713_2_, int p_76713_3_) throws IOException
-    {
-        this.chunkTimestamps[p_76713_1_ + p_76713_2_ * 32] = p_76713_3_;
-        this.dataFile.seek((long)(4096 + (p_76713_1_ + p_76713_2_ * 32) * 4));
-        this.dataFile.writeInt(p_76713_3_);
+    private void setChunkTimestamp(int x, int z, int timestamp) throws IOException {
+        this.chunkTimestamps[x + z * 32] = timestamp;
+        this.dataFile.seek(4096 + (x + z * 32) * 4);
+        this.dataFile.writeInt(timestamp);
     }
 
-    public void close() throws IOException
-    {
-        if (this.dataFile != null)
-        {
+    public void close() throws IOException {
+        if (this.dataFile != null) {
             this.dataFile.close();
         }
     }
 
-    class ChunkBuffer extends ByteArrayOutputStream
-    {
-        private int chunkX;
-        private int chunkZ;
+    class ChunkBuffer extends ByteArrayOutputStream {
+        private final int chunkX;
+        private final int chunkZ;
         private static final String __OBFID = "CL_00000382";
 
-        public ChunkBuffer(int p_i2000_2_, int p_i2000_3_)
-        {
+        public ChunkBuffer(int x, int z) {
             super(8096);
-            this.chunkX = p_i2000_2_;
-            this.chunkZ = p_i2000_3_;
+            this.chunkX = x;
+            this.chunkZ = z;
         }
 
-        public void close() throws IOException
-        {
+        public void close() throws IOException {
             RegionFile.this.write(this.chunkX, this.chunkZ, this.buf, this.count);
         }
     }
