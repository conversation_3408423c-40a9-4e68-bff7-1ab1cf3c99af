--- ../src-base/minecraft/net/minecraft/world/Teleporter.java
+++ ../src-work/minecraft/net/minecraft/world/Teleporter.java
@@ -12,6 +12,12 @@
 import net.minecraft.util.LongHashMap;
 import net.minecraft.util.MathHelper;
 
+// CraftBukkit start
+import org.bukkit.Location;
+import org.bukkit.event.entity.EntityPortalExitEvent;
+import org.bukkit.util.Vector;
+// CraftBukkit end
+
 public class Teleporter
 {
     private final WorldServer worldServerInstance;
@@ -20,100 +26,172 @@
     private final List destinationCoordinateKeys = new ArrayList();
     private static final String __OBFID = "CL_00000153";
 
-    public Teleporter(WorldServer p_i1963_1_)
+    public Teleporter(WorldServer par1WorldServer)
     {
-        this.worldServerInstance = p_i1963_1_;
-        this.random = new Random(p_i1963_1_.getSeed());
+        this.worldServerInstance = par1WorldServer;
+        this.random = new Random(par1WorldServer.getSeed());
     }
 
-    public void placeInPortal(Entity p_77185_1_, double p_77185_2_, double p_77185_4_, double p_77185_6_, float p_77185_8_)
+    public void placeInPortal(Entity par1Entity, double par2, double par4, double par6, float par8)
     {
         if (this.worldServerInstance.provider.dimensionId != 1)
         {
-            if (!this.placeInExistingPortal(p_77185_1_, p_77185_2_, p_77185_4_, p_77185_6_, p_77185_8_))
+            if (!this.placeInExistingPortal(par1Entity, par2, par4, par6, par8))
             {
-                this.makePortal(p_77185_1_);
-                this.placeInExistingPortal(p_77185_1_, p_77185_2_, p_77185_4_, p_77185_6_, p_77185_8_);
+                this.makePortal(par1Entity);
+                this.placeInExistingPortal(par1Entity, par2, par4, par6, par8);
             }
         }
         else
         {
-            int i = MathHelper.floor_double(p_77185_1_.posX);
-            int j = MathHelper.floor_double(p_77185_1_.posY) - 1;
-            int k = MathHelper.floor_double(p_77185_1_.posZ);
-            byte b0 = 1;
-            byte b1 = 0;
+            // CraftBukkit start - Modularize end portal creation
+            ChunkCoordinates created = this.createEndPortal(par2, par4, par6);
+            par1Entity.setLocationAndAngles((double) created.posX, (double) created.posY, (double) created.posZ, par1Entity.rotationYaw, 0.0F);
+            par1Entity.motionX = par1Entity.motionY = par1Entity.motionZ = 0.0D;
+        }
+    }
 
-            for (int l = -2; l <= 2; ++l)
+    // Split out from original a(Entity, double, double, double, float) method in order to enable being called from createPortal
+    private ChunkCoordinates createEndPortal(double x, double y, double z)
+    {
+        int i = MathHelper.floor_double(x);
+        int j = MathHelper.floor_double(y) - 1;
+        int k = MathHelper.floor_double(z);
+        // CraftBukkit end
+        byte b0 = 1;
+        byte b1 = 0;
+
+        for (int l = -2; l <= 2; ++l)
+        {
+            for (int i1 = -2; i1 <= 2; ++i1)
             {
-                for (int i1 = -2; i1 <= 2; ++i1)
+                for (int j1 = -1; j1 < 3; ++j1)
                 {
-                    for (int j1 = -1; j1 < 3; ++j1)
+                    int k1 = i + i1 * b0 + l * b1;
+                    int l1 = j + j1;
+                    int i2 = k + i1 * b1 - l * b0;
+                    boolean flag = j1 < 0;
+                    this.worldServerInstance.setBlock(k1, l1, i2, flag ? Blocks.obsidian : Blocks.air);
+                }
+            }
+        }
+
+        // CraftBukkit start
+        return new ChunkCoordinates(i, j, k);
+    }
+
+    // use logic based on creation to verify end portal
+    private ChunkCoordinates findEndPortal(ChunkCoordinates portal)
+    {
+        int i = portal.posX;
+        int j = portal.posY - 1;
+        int k = portal.posZ;
+        byte b0 = 1;
+        byte b1 = 0;
+
+        for (int l = -2; l <= 2; ++l)
+        {
+            for (int i1 = -2; i1 <= 2; ++i1)
+            {
+                for (int j1 = -1; j1 < 3; ++j1)
+                {
+                    int k1 = i + i1 * b0 + l * b1;
+                    int l1 = j + j1;
+                    int i2 = k + i1 * b1 - l * b0;
+                    boolean flag = j1 < 0;
+
+                    if (this.worldServerInstance.getBlock(k1, l1, i2) != (flag ? Blocks.obsidian : Blocks.air))
                     {
-                        int k1 = i + i1 * b0 + l * b1;
-                        int l1 = j + j1;
-                        int i2 = k + i1 * b1 - l * b0;
-                        boolean flag = j1 < 0;
-                        this.worldServerInstance.setBlock(k1, l1, i2, flag ? Blocks.obsidian : Blocks.air);
+                        return null;
                     }
                 }
             }
+        }
 
-            p_77185_1_.setLocationAndAngles((double)i, (double)j, (double)k, p_77185_1_.rotationYaw, 0.0F);
-            p_77185_1_.motionX = p_77185_1_.motionY = p_77185_1_.motionZ = 0.0D;
+        return new ChunkCoordinates(i, j, k);
+    }
+    // CraftBukkit end
+
+    public boolean placeInExistingPortal(Entity par1Entity, double par2, double par4, double par6, float par8)
+    {
+        // CraftBukkit start - Modularize portal search process and entity teleportation
+        ChunkCoordinates found = this.findPortal(par1Entity.posX, par1Entity.posY, par1Entity.posZ, 128);
+
+        if (found == null)
+        {
+            return false;
         }
+
+        Location exit = new Location(this.worldServerInstance.getWorld(), found.posX, found.posY, found.posZ, par8, par1Entity.rotationPitch);
+        Vector velocity = par1Entity.getBukkitEntity().getVelocity();
+        this.adjustExit(par1Entity, exit, velocity);
+        par1Entity.setLocationAndAngles(exit.getX(), exit.getY(), exit.getZ(), exit.getYaw(), exit.getPitch());
+
+        if (par1Entity.motionX != velocity.getX() || par1Entity.motionY != velocity.getY() || par1Entity.motionZ != velocity.getZ())
+        {
+            par1Entity.getBukkitEntity().setVelocity(velocity);
+        }
+
+        return true;
     }
 
-    public boolean placeInExistingPortal(Entity p_77184_1_, double p_77184_2_, double p_77184_4_, double p_77184_6_, float p_77184_8_)
+    public ChunkCoordinates findPortal(double x, double y, double z, int short1)
     {
-        short short1 = 128;
+        if (this.worldServerInstance.getWorld().getEnvironment() == org.bukkit.World.Environment.THE_END)
+        {
+            return this.findEndPortal(this.worldServerInstance.provider.getEntrancePortalLocation());
+        }
+
+        // CraftBukkit end
         double d3 = -1.0D;
         int i = 0;
         int j = 0;
         int k = 0;
-        int l = MathHelper.floor_double(p_77184_1_.posX);
-        int i1 = MathHelper.floor_double(p_77184_1_.posZ);
+        // CraftBukkit start
+        int l = MathHelper.floor_double(x);
+        int i1 = MathHelper.floor_double(z);
+        // CraftBukkit end
         long j1 = ChunkCoordIntPair.chunkXZ2Int(l, i1);
         boolean flag = true;
-        double d7;
-        int l3;
+        double d4;
+        int k1;
 
         if (this.destinationCoordinateCache.containsItem(j1))
         {
-            Teleporter.PortalPosition portalposition = (Teleporter.PortalPosition)this.destinationCoordinateCache.getValueByKey(j1);
+            PortalPosition chunkcoordinatesportal = (PortalPosition) this.destinationCoordinateCache.getValueByKey(j1);
             d3 = 0.0D;
-            i = portalposition.posX;
-            j = portalposition.posY;
-            k = portalposition.posZ;
-            portalposition.lastUpdateTime = this.worldServerInstance.getTotalWorldTime();
+            i = chunkcoordinatesportal.posX;
+            j = chunkcoordinatesportal.posY;
+            k = chunkcoordinatesportal.posZ;
+            chunkcoordinatesportal.lastUpdateTime = this.worldServerInstance.getTotalWorldTime();
             flag = false;
         }
         else
         {
-            for (l3 = l - short1; l3 <= l + short1; ++l3)
+            for (k1 = l - short1; k1 <= l + short1; ++k1)
             {
-                double d4 = (double)l3 + 0.5D - p_77184_1_.posX;
+                double d5 = (double) k1 + 0.5D - x; // CraftBukkit
 
                 for (int l1 = i1 - short1; l1 <= i1 + short1; ++l1)
                 {
-                    double d5 = (double)l1 + 0.5D - p_77184_1_.posZ;
+                    double d6 = (double) l1 + 0.5D - z; // CraftBukkit
 
                     for (int i2 = this.worldServerInstance.getActualHeight() - 1; i2 >= 0; --i2)
                     {
-                        if (this.worldServerInstance.getBlock(l3, i2, l1) == Blocks.portal)
+                        if (this.worldServerInstance.getBlock(k1, i2, l1) == Blocks.portal)
                         {
-                            while (this.worldServerInstance.getBlock(l3, i2 - 1, l1) == Blocks.portal)
+                            while (this.worldServerInstance.getBlock(k1, i2 - 1, l1) == Blocks.portal)
                             {
                                 --i2;
                             }
 
-                            d7 = (double)i2 + 0.5D - p_77184_1_.posY;
-                            double d8 = d4 * d4 + d7 * d7 + d5 * d5;
+                            d4 = (double) i2 + 0.5D - y; // CraftBukkit
+                            double d7 = d5 * d5 + d4 * d4 + d6 * d6;
 
-                            if (d3 < 0.0D || d8 < d3)
+                            if (d3 < 0.0D || d7 < d3)
                             {
-                                d3 = d8;
-                                i = l3;
+                                d3 = d7;
+                                i = k1;
                                 j = i2;
                                 k = l1;
                             }
@@ -127,61 +205,93 @@
         {
             if (flag)
             {
-                this.destinationCoordinateCache.add(j1, new Teleporter.PortalPosition(i, j, k, this.worldServerInstance.getTotalWorldTime()));
+                this.destinationCoordinateCache.add(j1, new PortalPosition(i, j, k, this.worldServerInstance.getTotalWorldTime()));
                 this.destinationCoordinateKeys.add(Long.valueOf(j1));
             }
 
-            double d11 = (double)i + 0.5D;
-            double d6 = (double)j + 0.5D;
-            d7 = (double)k + 0.5D;
-            int i4 = -1;
+            // CraftBukkit start - Moved entity teleportation logic into exit
+            return new ChunkCoordinates(i, j, k);
+        }
+        else
+        {
+            return null;
+        }
+    }
+    // Entity repositioning logic split out from original b method and combined with repositioning logic for The End from original a method
+    public void adjustExit(Entity entity, Location position, Vector velocity)
+    {
+        Location from = position.clone();
+        Vector before = velocity.clone();
+        int i = position.getBlockX();
+        int j = position.getBlockY();
+        int k = position.getBlockZ();
+        float f = position.getYaw();
 
+        if (this.worldServerInstance.getWorld().getEnvironment() == org.bukkit.World.Environment.THE_END)
+        {
+            // entity.setPositionRotation((double) i, (double) j, (double) k, entity.yaw, 0.0F);
+            // entity.motX = entity.motY = entity.motZ = 0.0D;
+            position.setPitch(0.0F);
+            velocity.setX(0);
+            velocity.setY(0);
+            velocity.setZ(0);
+        }
+        else
+        {
+            double d4;
+            int k1;
+            // CraftBukkit end
+            double d8 = (double) i + 0.5D;
+            double d9 = (double) j + 0.5D;
+            d4 = (double) k + 0.5D;
+            int j2 = -1;
+
             if (this.worldServerInstance.getBlock(i - 1, j, k) == Blocks.portal)
             {
-                i4 = 2;
+                j2 = 2;
             }
 
             if (this.worldServerInstance.getBlock(i + 1, j, k) == Blocks.portal)
             {
-                i4 = 0;
+                j2 = 0;
             }
 
             if (this.worldServerInstance.getBlock(i, j, k - 1) == Blocks.portal)
             {
-                i4 = 3;
+                j2 = 3;
             }
 
             if (this.worldServerInstance.getBlock(i, j, k + 1) == Blocks.portal)
             {
-                i4 = 1;
+                j2 = 1;
             }
 
-            int j2 = p_77184_1_.getTeleportDirection();
+            int k2 = entity.getTeleportDirection();
 
-            if (i4 > -1)
+            if (j2 > -1)
             {
-                int k2 = Direction.rotateLeft[i4];
-                int l2 = Direction.offsetX[i4];
-                int i3 = Direction.offsetZ[i4];
-                int j3 = Direction.offsetX[k2];
-                int k3 = Direction.offsetZ[k2];
-                boolean flag1 = !this.worldServerInstance.isAirBlock(i + l2 + j3, j, k + i3 + k3) || !this.worldServerInstance.isAirBlock(i + l2 + j3, j + 1, k + i3 + k3);
-                boolean flag2 = !this.worldServerInstance.isAirBlock(i + l2, j, k + i3) || !this.worldServerInstance.isAirBlock(i + l2, j + 1, k + i3);
+                int l2 = Direction.rotateLeft[j2];
+                int i3 = Direction.offsetX[j2];
+                int j3 = Direction.offsetZ[j2];
+                int k3 = Direction.offsetX[l2];
+                int l3 = Direction.offsetZ[l2];
+                boolean flag1 = !this.worldServerInstance.isAirBlock(i + i3 + k3, j, k + j3 + l3) || !this.worldServerInstance.isAirBlock(i + i3 + k3, j + 1, k + j3 + l3);
+                boolean flag2 = !this.worldServerInstance.isAirBlock(i + i3, j, k + j3) || !this.worldServerInstance.isAirBlock(i + i3, j + 1, k + j3);
 
                 if (flag1 && flag2)
                 {
-                    i4 = Direction.rotateOpposite[i4];
-                    k2 = Direction.rotateOpposite[k2];
-                    l2 = Direction.offsetX[i4];
-                    i3 = Direction.offsetZ[i4];
-                    j3 = Direction.offsetX[k2];
-                    k3 = Direction.offsetZ[k2];
-                    l3 = i - j3;
-                    d11 -= (double)j3;
-                    int k1 = k - k3;
-                    d7 -= (double)k3;
-                    flag1 = !this.worldServerInstance.isAirBlock(l3 + l2 + j3, j, k1 + i3 + k3) || !this.worldServerInstance.isAirBlock(l3 + l2 + j3, j + 1, k1 + i3 + k3);
-                    flag2 = !this.worldServerInstance.isAirBlock(l3 + l2, j, k1 + i3) || !this.worldServerInstance.isAirBlock(l3 + l2, j + 1, k1 + i3);
+                    j2 = Direction.rotateOpposite[j2];
+                    l2 = Direction.rotateOpposite[l2];
+                    i3 = Direction.offsetX[j2];
+                    j3 = Direction.offsetZ[j2];
+                    k3 = Direction.offsetX[l2];
+                    l3 = Direction.offsetZ[l2];
+                    k1 = i - k3;
+                    d8 -= (double) k3;
+                    int i4 = k - l3;
+                    d4 -= (double) l3;
+                    flag1 = !this.worldServerInstance.isAirBlock(k1 + i3 + k3, j, i4 + j3 + l3) || !this.worldServerInstance.isAirBlock(k1 + i3 + k3, j + 1, i4 + j3 + l3);
+                    flag2 = !this.worldServerInstance.isAirBlock(k1 + i3, j, i4 + j3) || !this.worldServerInstance.isAirBlock(k1 + i3, j + 1, i4 + j3);
                 }
 
                 float f1 = 0.5F;
@@ -200,24 +310,24 @@
                     f2 = 0.0F;
                 }
 
-                d11 += (double)((float)j3 * f1 + f2 * (float)l2);
-                d7 += (double)((float)k3 * f1 + f2 * (float)i3);
+                d8 += (double)((float) k3 * f1 + f2 * (float) i3);
+                d4 += (double)((float) l3 * f1 + f2 * (float) j3);
                 float f3 = 0.0F;
                 float f4 = 0.0F;
                 float f5 = 0.0F;
                 float f6 = 0.0F;
 
-                if (i4 == j2)
+                if (j2 == k2)
                 {
                     f3 = 1.0F;
                     f4 = 1.0F;
                 }
-                else if (i4 == Direction.rotateOpposite[j2])
+                else if (j2 == Direction.rotateOpposite[k2])
                 {
                     f3 = -1.0F;
                     f4 = -1.0F;
                 }
-                else if (i4 == Direction.rotateRight[j2])
+                else if (j2 == Direction.rotateRight[k2])
                 {
                     f5 = 1.0F;
                     f6 = -1.0F;
@@ -228,33 +338,77 @@
                     f6 = 1.0F;
                 }
 
-                double d9 = p_77184_1_.motionX;
-                double d10 = p_77184_1_.motionZ;
-                p_77184_1_.motionX = d9 * (double)f3 + d10 * (double)f6;
-                p_77184_1_.motionZ = d9 * (double)f5 + d10 * (double)f4;
-                p_77184_1_.rotationYaw = p_77184_8_ - (float)(j2 * 90) + (float)(i4 * 90);
+                // CraftBukkit start
+                double d10 = velocity.getX();
+                double d11 = velocity.getZ();
+                // CraftBukkit end
+                // CraftBukkit start - Adjust position and velocity instances instead of entity
+                velocity.setX(d10 * (double) f3 + d11 * (double) f6);
+                velocity.setZ(d10 * (double) f5 + d11 * (double) f4);
+                f = f - (float)(k2 * 90) + (float)(j2 * 90);
             }
             else
             {
-                p_77184_1_.motionX = p_77184_1_.motionY = p_77184_1_.motionZ = 0.0D;
+                // entity.motX = entity.motY = entity.motZ = 0.0D;
+                velocity.setX(0);
+                velocity.setY(0);
+                velocity.setZ(0);
             }
 
-            p_77184_1_.setLocationAndAngles(d11, d6, d7, p_77184_1_.rotationYaw, p_77184_1_.rotationPitch);
-            return true;
+            // entity.setPositionRotation(d8, d9, d4, entity.yaw, entity.pitch);
+            position.setX(d8);
+            position.setY(d9);
+            position.setZ(d4);
+            position.setYaw(f);
         }
+
+        EntityPortalExitEvent event = new EntityPortalExitEvent(entity.getBukkitEntity(), from, position, before, velocity);
+        this.worldServerInstance.getServer().getPluginManager().callEvent(event);
+        Location to = event.getTo();
+
+        if (event.isCancelled() || to == null || !entity.isEntityAlive())
+        {
+            position.setX(from.getX());
+            position.setY(from.getY());
+            position.setZ(from.getZ());
+            position.setYaw(from.getYaw());
+            position.setPitch(from.getPitch());
+            velocity.copy(before);
+        }
         else
         {
-            return false;
+            position.setX(to.getX());
+            position.setY(to.getY());
+            position.setZ(to.getZ());
+            position.setYaw(to.getYaw());
+            position.setPitch(to.getPitch());
+            velocity.copy(event.getAfter()); // event.getAfter() will never be null, as setAfter() will cause an NPE if null is passed in
         }
+
+        // CraftBukkit end
     }
 
-    public boolean makePortal(Entity p_85188_1_)
+    public boolean makePortal(Entity par1Entity)
     {
-        byte b0 = 16;
+        // CraftBukkit start - Allow for portal creation to be based on coordinates instead of entity
+        return this.createPortal(par1Entity.posX, par1Entity.posY, par1Entity.posZ, 16);
+    }
+
+    public boolean createPortal(double x, double y, double z, int b0)
+    {
+        if (this.worldServerInstance.getWorld().getEnvironment() == org.bukkit.World.Environment.THE_END)
+        {
+            this.createEndPortal(x, y, z);
+            return true;
+        }
+ 
+        // CraftBukkit end
         double d0 = -1.0D;
-        int i = MathHelper.floor_double(p_85188_1_.posX);
-        int j = MathHelper.floor_double(p_85188_1_.posY);
-        int k = MathHelper.floor_double(p_85188_1_.posZ);
+        // CraftBukkit start
+        int i = MathHelper.floor_double(x);
+        int j = MathHelper.floor_double(y);
+        int k = MathHelper.floor_double(z);
+        // CraftBukkit end
         int l = i;
         int i1 = j;
         int j1 = k;
@@ -262,8 +416,10 @@
         int l1 = this.random.nextInt(4);
         int i2;
         double d1;
-        int k2;
         double d2;
+        int j2;
+        int k2;
+        int l2;
         int i3;
         int j3;
         int k3;
@@ -271,125 +427,123 @@
         int i4;
         int j4;
         int k4;
-        int l4;
-        int i5;
         double d3;
         double d4;
-
+ 
         for (i2 = i - b0; i2 <= i + b0; ++i2)
         {
-            d1 = (double)i2 + 0.5D - p_85188_1_.posX;
-
-            for (k2 = k - b0; k2 <= k + b0; ++k2)
+            d1 = (double) i2 + 0.5D - x; // CraftBukkit
+ 
+            for (j2 = k - b0; j2 <= k + b0; ++j2)
             {
-                d2 = (double)k2 + 0.5D - p_85188_1_.posZ;
+                d2 = (double) j2 + 0.5D - z; // CraftBukkit
                 label274:
-
-                for (i3 = this.worldServerInstance.getActualHeight() - 1; i3 >= 0; --i3)
+ 
+                for (k2 = this.worldServerInstance.getActualHeight() - 1; k2 >= 0; --k2)
                 {
-                    if (this.worldServerInstance.isAirBlock(i2, i3, k2))
+                    if (this.worldServerInstance.isAirBlock(i2, k2, j2))
                     {
-                        while (i3 > 0 && this.worldServerInstance.isAirBlock(i2, i3 - 1, k2))
+                        while (k2 > 0 && this.worldServerInstance.isAirBlock(i2, k2 - 1, j2))
                         {
-                            --i3;
+                            --k2;
                         }
-
-                        for (j3 = l1; j3 < l1 + 4; ++j3)
+ 
+                        for (i3 = l1; i3 < l1 + 4; ++i3)
                         {
-                            k3 = j3 % 2;
-                            l3 = 1 - k3;
-
-                            if (j3 % 4 >= 2)
+                            l2 = i3 % 2;
+                            k3 = 1 - l2;
+ 
+                            if (i3 % 4 >= 2)
                             {
+                                l2 = -l2;
                                 k3 = -k3;
-                                l3 = -l3;
                             }
-
-                            for (i4 = 0; i4 < 3; ++i4)
+ 
+                            for (j3 = 0; j3 < 3; ++j3)
                             {
-                                for (j4 = 0; j4 < 4; ++j4)
+                                for (i4 = 0; i4 < 4; ++i4)
                                 {
-                                    for (k4 = -1; k4 < 4; ++k4)
+                                    for (l3 = -1; l3 < 4; ++l3)
                                     {
-                                        l4 = i2 + (j4 - 1) * k3 + i4 * l3;
-                                        i5 = i3 + k4;
-                                        int j5 = k2 + (j4 - 1) * l3 - i4 * k3;
-
-                                        if (k4 < 0 && !this.worldServerInstance.getBlock(l4, i5, j5).getMaterial().isSolid() || k4 >= 0 && !this.worldServerInstance.isAirBlock(l4, i5, j5))
+                                        k4 = i2 + (i4 - 1) * l2 + j3 * k3;
+                                        j4 = k2 + l3;
+                                        int l4 = j2 + (i4 - 1) * k3 - j3 * l2;
+ 
+                                        if (l3 < 0 && !this.worldServerInstance.getBlock(k4, j4, l4).getMaterial().isSolid() || l3 >= 0 && !this.worldServerInstance.isAirBlock(k4, j4, l4))
                                         {
                                             continue label274;
                                         }
                                     }
                                 }
                             }
-
-                            d3 = (double)i3 + 0.5D - p_85188_1_.posY;
+ 
+                            d3 = (double) k2 + 0.5D - y; // CraftBukkit
                             d4 = d1 * d1 + d3 * d3 + d2 * d2;
-
+ 
                             if (d0 < 0.0D || d4 < d0)
                             {
                                 d0 = d4;
                                 l = i2;
-                                i1 = i3;
-                                j1 = k2;
-                                k1 = j3 % 4;
+                                i1 = k2;
+                                j1 = j2;
+                                k1 = i3 % 4;
                             }
                         }
                     }
                 }
             }
         }
-
+ 
         if (d0 < 0.0D)
         {
             for (i2 = i - b0; i2 <= i + b0; ++i2)
             {
-                d1 = (double)i2 + 0.5D - p_85188_1_.posX;
-
-                for (k2 = k - b0; k2 <= k + b0; ++k2)
+                d1 = (double) i2 + 0.5D - x; // CraftBukkit
+ 
+                for (j2 = k - b0; j2 <= k + b0; ++j2)
                 {
-                    d2 = (double)k2 + 0.5D - p_85188_1_.posZ;
+                    d2 = (double) j2 + 0.5D - z; // CraftBukkit
                     label222:
-
-                    for (i3 = this.worldServerInstance.getActualHeight() - 1; i3 >= 0; --i3)
+ 
+                    for (k2 = this.worldServerInstance.getActualHeight() - 1; k2 >= 0; --k2)
                     {
-                        if (this.worldServerInstance.isAirBlock(i2, i3, k2))
+                        if (this.worldServerInstance.isAirBlock(i2, k2, j2))
                         {
-                            while (i3 > 0 && this.worldServerInstance.isAirBlock(i2, i3 - 1, k2))
+                            while (k2 > 0 && this.worldServerInstance.isAirBlock(i2, k2 - 1, j2))
                             {
-                                --i3;
+                                --k2;
                             }
-
-                            for (j3 = l1; j3 < l1 + 2; ++j3)
+ 
+                            for (i3 = l1; i3 < l1 + 2; ++i3)
                             {
-                                k3 = j3 % 2;
-                                l3 = 1 - k3;
-
-                                for (i4 = 0; i4 < 4; ++i4)
+                                l2 = i3 % 2;
+                                k3 = 1 - l2;
+ 
+                                for (j3 = 0; j3 < 4; ++j3)
                                 {
-                                    for (j4 = -1; j4 < 4; ++j4)
+                                    for (i4 = -1; i4 < 4; ++i4)
                                     {
-                                        k4 = i2 + (i4 - 1) * k3;
-                                        l4 = i3 + j4;
-                                        i5 = k2 + (i4 - 1) * l3;
-
-                                        if (j4 < 0 && !this.worldServerInstance.getBlock(k4, l4, i5).getMaterial().isSolid() || j4 >= 0 && !this.worldServerInstance.isAirBlock(k4, l4, i5))
+                                        l3 = i2 + (j3 - 1) * l2;
+                                        k4 = k2 + i4;
+                                        j4 = j2 + (j3 - 1) * k3;
+ 
+                                        if (i4 < 0 && !this.worldServerInstance.getBlock(l3, k4, j4).getMaterial().isSolid() || i4 >= 0 && !this.worldServerInstance.isAirBlock(l3, k4, j4))
                                         {
                                             continue label222;
                                         }
                                     }
                                 }
-
-                                d3 = (double)i3 + 0.5D - p_85188_1_.posY;
+ 
+                                d3 = (double) k2 + 0.5D - y; // CraftBukkit
                                 d4 = d1 * d1 + d3 * d3 + d2 * d2;
-
+ 
                                 if (d0 < 0.0D || d4 < d0)
                                 {
                                     d0 = d4;
                                     l = i2;
-                                    i1 = i3;
-                                    j1 = k2;
-                                    k1 = j3 % 2;
+                                    i1 = k2;
+                                    j1 = j2;
+                                    k1 = i3 % 2;
                                 }
                             }
                         }
@@ -397,93 +551,93 @@
                 }
             }
         }
-
-        int k5 = l;
-        int j2 = i1;
-        k2 = j1;
-        int l5 = k1 % 2;
-        int l2 = 1 - l5;
-
+ 
+        int i5 = l;
+        int j5 = i1;
+        j2 = j1;
+        int k5 = k1 % 2;
+        int l5 = 1 - k5;
+ 
         if (k1 % 4 >= 2)
         {
+            k5 = -k5;
             l5 = -l5;
-            l2 = -l2;
         }
-
+ 
         boolean flag;
-
+ 
         if (d0 < 0.0D)
         {
             if (i1 < 70)
             {
                 i1 = 70;
             }
-
+ 
             if (i1 > this.worldServerInstance.getActualHeight() - 10)
             {
                 i1 = this.worldServerInstance.getActualHeight() - 10;
             }
-
-            j2 = i1;
-
-            for (i3 = -1; i3 <= 1; ++i3)
+ 
+            j5 = i1;
+ 
+            for (k2 = -1; k2 <= 1; ++k2)
             {
-                for (j3 = 1; j3 < 3; ++j3)
+                for (i3 = 1; i3 < 3; ++i3)
                 {
-                    for (k3 = -1; k3 < 3; ++k3)
+                    for (l2 = -1; l2 < 3; ++l2)
                     {
-                        l3 = k5 + (j3 - 1) * l5 + i3 * l2;
-                        i4 = j2 + k3;
-                        j4 = k2 + (j3 - 1) * l2 - i3 * l5;
-                        flag = k3 < 0;
-                        this.worldServerInstance.setBlock(l3, i4, j4, flag ? Blocks.obsidian : Blocks.air);
+                        k3 = i5 + (i3 - 1) * k5 + k2 * l5;
+                        j3 = j5 + l2;
+                        i4 = j2 + (i3 - 1) * l5 - k2 * k5;
+                        flag = l2 < 0;
+                        this.worldServerInstance.setBlock(k3, j3, i4, flag ? Blocks.obsidian : Blocks.air);
                     }
                 }
             }
         }
-
-        for (i3 = 0; i3 < 4; ++i3)
+ 
+        for (k2 = 0; k2 < 4; ++k2)
         {
-            for (j3 = 0; j3 < 4; ++j3)
+            for (i3 = 0; i3 < 4; ++i3)
             {
-                for (k3 = -1; k3 < 4; ++k3)
+                for (l2 = -1; l2 < 4; ++l2)
                 {
-                    l3 = k5 + (j3 - 1) * l5;
-                    i4 = j2 + k3;
-                    j4 = k2 + (j3 - 1) * l2;
-                    flag = j3 == 0 || j3 == 3 || k3 == -1 || k3 == 3;
-                    this.worldServerInstance.setBlock(l3, i4, j4, (Block)(flag ? Blocks.obsidian : Blocks.portal), 0, 2);
+                    k3 = i5 + (i3 - 1) * k5;
+                    j3 = j5 + l2;
+                    i4 = j2 + (i3 - 1) * l5;
+                    flag = i3 == 0 || i3 == 3 || l2 == -1 || l2 == 3;
+                    this.worldServerInstance.setBlock(k3, j3, i4, flag ? Blocks.obsidian : Blocks.portal, 0, 2);
                 }
             }
-
-            for (j3 = 0; j3 < 4; ++j3)
+ 
+            for (i3 = 0; i3 < 4; ++i3)
             {
-                for (k3 = -1; k3 < 4; ++k3)
+                for (l2 = -1; l2 < 4; ++l2)
                 {
-                    l3 = k5 + (j3 - 1) * l5;
-                    i4 = j2 + k3;
-                    j4 = k2 + (j3 - 1) * l2;
-                    this.worldServerInstance.notifyBlocksOfNeighborChange(l3, i4, j4, this.worldServerInstance.getBlock(l3, i4, j4));
+                    k3 = i5 + (i3 - 1) * k5;
+                    j3 = j5 + l2;
+                    i4 = j2 + (i3 - 1) * l5;
+                    this.worldServerInstance.notifyBlocksOfNeighborChange(k3, j3, i4, this.worldServerInstance.getBlock(k3, j3, i4));
                 }
             }
         }
-
+ 
         return true;
     }
 
-    public void removeStalePortalLocations(long p_85189_1_)
+    public void removeStalePortalLocations(long par1)
     {
-        if (p_85189_1_ % 100L == 0L)
+        if (par1 % 100L == 0L)
         {
             Iterator iterator = this.destinationCoordinateKeys.iterator();
-            long j = p_85189_1_ - 600L;
-
+            long j = par1 - 600L;
+ 
             while (iterator.hasNext())
             {
-                Long olong = (Long)iterator.next();
-                Teleporter.PortalPosition portalposition = (Teleporter.PortalPosition)this.destinationCoordinateCache.getValueByKey(olong.longValue());
-
-                if (portalposition == null || portalposition.lastUpdateTime < j)
+                Long olong = (Long) iterator.next();
+                PortalPosition chunkcoordinatesportal = (PortalPosition) this.destinationCoordinateCache.getValueByKey(olong.longValue());
+ 
+                if (chunkcoordinatesportal == null || chunkcoordinatesportal.lastUpdateTime < j)
                 {
                     iterator.remove();
                     this.destinationCoordinateCache.remove(olong.longValue());
@@ -491,16 +645,16 @@
             }
         }
     }
-
+ 
     public class PortalPosition extends ChunkCoordinates
     {
         public long lastUpdateTime;
         private static final String __OBFID = "CL_00000154";
-
-        public PortalPosition(int p_i1962_2_, int p_i1962_3_, int p_i1962_4_, long p_i1962_5_)
+ 
+        public PortalPosition(int par2, int par3, int par4, long par5)
         {
-            super(p_i1962_2_, p_i1962_3_, p_i1962_4_);
-            this.lastUpdateTime = p_i1962_5_;
+            super(par2, par3, par4);
+            this.lastUpdateTime = par5;
         }
     }
 }
