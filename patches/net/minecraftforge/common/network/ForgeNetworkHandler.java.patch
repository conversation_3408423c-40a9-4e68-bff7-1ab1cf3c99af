--- ../src-base/minecraft/net/minecraftforge/common/network/ForgeNetworkHandler.java
+++ ../src-work/minecraft/net/minecraftforge/common/network/ForgeNetworkHandler.java
@@ -35,4 +35,9 @@
         clientChannel.pipeline().addAfter(handlerName, "DimensionHandler", new DimensionMessageHandler());
         clientChannel.pipeline().addAfter(handlerName, "FluidIdRegistryHandler", new FluidIdRegistryMessageHandler());
     }
+
+    public static FMLEmbeddedChannel getServerChannel()
+    {
+        return channelPair.get(Side.SERVER);
+    }
 }
