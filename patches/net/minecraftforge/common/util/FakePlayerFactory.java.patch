--- ../src-base/minecraft/net/minecraftforge/common/util/FakePlayerFactory.java
+++ ../src-work/minecraft/net/minecraftforge/common/util/FakePlayerFactory.java
@@ -16,7 +16,7 @@
 {
     private static GameProfile MINECRAFT = new GameProfile(UUID.fromString("41C82C87-7AfB-4024-BA57-13D2C99CAE77"), "[Minecraft]");
     // Map of all active fake player usernames to their entities
-    private static Map<GameProfile, FakePlayer> fakePlayers = Maps.newHashMap();
+    public static Map<GameProfile, FakePlayer> fakePlayers = Maps.newHashMap();
     private static FakePlayer MINECRAFT_PLAYER = null;
     
     public static FakePlayer getMinecraft(WorldServer world)
@@ -35,12 +35,24 @@
      */
     public static FakePlayer get(WorldServer world, GameProfile username)
     {
-        if (!fakePlayers.contains<PERSON>ey(username))
+        // Cauldron start - Refactored below to avoid a hashCode check with a null GameProfile ID
+        if (username == null || username.getName() == null) return null;
+
+        for (Map.Entry<GameProfile, FakePlayer> mapEntry : fakePlayers.entrySet())
         {
-            FakePlayer fakePlayer = new FakePlayer(world, username);
-            fakePlayers.put(username, fakePlayer);
+            GameProfile gameprofile = mapEntry.getKey();
+            if (gameprofile.getName().equals(username.getName()))
+            {
+                return mapEntry.getValue();
+            }
         }
-
+        FakePlayer fakePlayer = new FakePlayer(world, username);
+        if (username.getId() == null) // GameProfile hashCode check will fail with a null ID
+        {
+            username = new GameProfile(UUID.randomUUID(), username.getName()); // Create new GameProfile with random UUID
+        }
+        // Cauldron end
+        fakePlayers.put(username, fakePlayer);
         return fakePlayers.get(username);
     }
 
