--- ../src-base/minecraft/net/minecraftforge/common/WorldSpecificSaveHandler.java
+++ ../src-work/minecraft/net/minecraftforge/common/WorldSpecificSaveHandler.java
@@ -1,6 +1,7 @@
 package net.minecraftforge.common;
 
 import java.io.File;
+import java.util.UUID;
 
 import net.minecraft.world.chunk.storage.IChunkLoader;
 import net.minecraft.world.storage.IPlayerFileData;
@@ -43,4 +44,11 @@
         return new File(dataDir, name + ".dat");
     }
 
+    // Cauldron start
+    @Override
+    public UUID getUUID()
+    {
+        return parent.getUUID();
+    }
+    // Cauldron end
 }
