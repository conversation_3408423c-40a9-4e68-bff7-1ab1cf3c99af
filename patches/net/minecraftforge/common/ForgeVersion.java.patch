--- ../src-base/minecraft/net/minecraftforge/common/ForgeVersion.java
+++ ../src-work/minecraft/net/minecraftforge/common/ForgeVersion.java
@@ -6,16 +6,8 @@
 package net.minecraftforge.common;
 import static net.minecraftforge.common.ForgeVersion.Status.*;
 
-import java.io.InputStream;
-import java.net.URL;
-import java.util.Map;
+import io.github.crucible.bootstrap.CrucibleMetadata;
 
-import com.google.common.io.ByteStreams;
-import com.google.gson.Gson;
-
-import cpw.mods.fml.common.versioning.ArtifactVersion;
-import cpw.mods.fml.common.versioning.DefaultArtifactVersion;
-
 public class ForgeVersion
 {
     //This number is incremented every time we remove deprecated code/major API changes, never reset
@@ -25,7 +17,7 @@
     //This number is incremented every time a interface changes or new major feature is added, and reset every Minecraft version
     public static final int revisionVersion = 4;
     //This number is incremented every time <PERSON> builds Forge, and never reset. Should always be 0 in the repo code.
-    public static final int buildVersion    = 0;
+    public static final int buildVersion    = CrucibleMetadata.FORGE_BUILD_VERSION;
 
     private static Status status = PENDING;
     private static String target = null;
@@ -78,71 +70,6 @@
 
     public static void startVersionCheck()
     {
-        new Thread("Forge Version Check")
-        {
-            @SuppressWarnings("unchecked")
-            @Override
-            public void run()
-            {
-                try
-                {
-                    URL url = new URL("http://files.minecraftforge.net/maven/net/minecraftforge/forge/promotions_slim.json");
-                    InputStream con = url.openStream();
-                    String data = new String(ByteStreams.toByteArray(con));
-                    con.close();
-
-                    Map<String, Object> json = new Gson().fromJson(data, Map.class);
-                    //String homepage = (String)json.get("homepage");
-                    Map<String, String> promos = (Map<String, String>)json.get("promos");
-
-                    String rec = promos.get(MinecraftForge.MC_VERSION + "-recommended");
-                    String lat = promos.get(MinecraftForge.MC_VERSION + "-latest");
-                    ArtifactVersion current = new DefaultArtifactVersion(getVersion());
-
-                    if (rec != null)
-                    {
-                        ArtifactVersion recommended = new DefaultArtifactVersion(rec);
-                        int diff = recommended.compareTo(current);
-
-                        if (diff == 0)
-                            status = UP_TO_DATE;
-                        else if (diff < 0)
-                        {
-                            status = AHEAD;
-                            if (lat != null)
-                            {
-                                if (current.compareTo(new DefaultArtifactVersion(lat)) < 0)
-                                {
-                                    status = OUTDATED;
-                                    target = lat;
-                                }
-                            }
-                        }
-                        else
-                        {
-                            status = OUTDATED;
-                            target = rec;
-                        }
-                    }
-                    else if (lat != null)
-                    {
-                        if (current.compareTo(new DefaultArtifactVersion(lat)) < 0)
-                        {
-                            status = BETA_OUTDATED;
-                            target = lat;
-                        }
-                        else
-                            status = BETA;
-                    }
-                    else
-                        status = BETA;
-                }
-                catch (Exception e)
-                {
-                    e.printStackTrace();
-                    status = FAILED;
-                }
-            }
-        }.start();
+        status = UP_TO_DATE; // Crucible - Forge changed how the version endpoint works, and there's no real need for checking the version, maybe reuse this for crucible's own version check.
     }
 }
