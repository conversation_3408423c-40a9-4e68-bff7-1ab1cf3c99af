--- ../src-base/minecraft/net/minecraftforge/oredict/ShapelessOreRecipe.java
+++ ../src-work/minecraft/net/minecraftforge/oredict/ShapelessOreRecipe.java
@@ -6,6 +6,10 @@
 import java.util.Map.Entry;
 import java.util.List;
 
+// Cauldron start
+import org.bukkit.inventory.Recipe;
+// Cauldron end
+
 import net.minecraft.block.Block;
 import net.minecraft.item.crafting.IRecipe;
 import net.minecraft.inventory.InventoryCrafting;
@@ -13,11 +17,13 @@
 import net.minecraft.item.ItemStack;
 import net.minecraft.item.crafting.ShapelessRecipes;
 import net.minecraft.world.World;
+import net.minecraftforge.cauldron.inventory.CustomModRecipe;
 
 public class ShapelessOreRecipe implements IRecipe
 {
     private ItemStack output = null;
     private ArrayList<Object> input = new ArrayList<Object>();
+    private ShapelessRecipes vanillaRecipe = null; // Cauldron - bukkit compatibility
 
     public ShapelessOreRecipe(Block result, Object... recipe){ this(new ItemStack(result), recipe); }
     public ShapelessOreRecipe(Item  result, Object... recipe){ this(new ItemStack(result), recipe); }
@@ -59,6 +65,7 @@
     @SuppressWarnings("unchecked")
     ShapelessOreRecipe(ShapelessRecipes recipe, Map<ItemStack, String> replacements)
     {
+        vanillaRecipe = recipe; // Cauldron - bukkit compatibility
         output = recipe.getRecipeOutput();
 
         for(ItemStack ingred : ((List<ItemStack>)recipe.recipeItems))
@@ -146,4 +153,13 @@
     {
         return this.input;
     }
+
+    // Cauldron start - required for Bukkit API
+    @Override
+    public Recipe toBukkitRecipe() {
+        if (vanillaRecipe != null)
+            return vanillaRecipe.toBukkitRecipe();
+        return new CustomModRecipe(this);
+    }
+    // Cauldron end
 }
