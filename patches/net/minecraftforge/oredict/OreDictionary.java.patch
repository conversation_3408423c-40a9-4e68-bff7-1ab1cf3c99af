--- ../src-base/minecraft/net/minecraftforge/oredict/OreDictionary.java
+++ ../src-work/minecraft/net/minecraftforge/oredict/OreDictionary.java
@@ -221,7 +221,7 @@
             {
                 ShapedRecipes recipe = (ShapedRecipes)obj;
                 ItemStack output = recipe.getRecipeOutput();
-                if (output != null && containsMatch(false, exclusions, output))
+                if ((output != null && containsMatch(false, exclusions, output)) || output == null) // Cauldron - fixes NPE's with null recipes being added to forge
                 {
                     continue;
                 }
@@ -236,7 +236,7 @@
             {
                 ShapelessRecipes recipe = (ShapelessRecipes)obj;
                 ItemStack output = recipe.getRecipeOutput();
-                if (output != null && containsMatch(false, exclusions, output))
+                if ((output != null && containsMatch(false, exclusions, output)) || output == null) // Cauldron - fixes NPE's with null recipes being added to forge
                 {
                     continue;
                 }
@@ -351,11 +351,28 @@
         {
             id = GameData.getItemRegistry().getId(registryName);
         }
+        //TODO: What that even means?
+//        Set<Integer> set;
+//        int count = 0;
+//        List<Integer> ids = stackToId.get(id);
+//        if (ids != null) count += ids.size();
+//        List<Integer> ids2 = stackToId.get(id | ((stack.getItemDamage() + 1) << 16));
+//        if (ids2 != null) count += ids2.size();
+//        set = new HashSet<Integer>(count);
+//
+//        boolean fal = ids != null ? set.addAll(ids): false;
+//        fal = ids2 != null ? set.addAll(ids2) : false;
+//
+//        int[] ret = new int[count];
+//        count = 0;
+//        for(Iterator<Integer> it = set.iterator(); it.hasNext();)
+//        {
+//            ret[count++] = it.next();
+//        }
         List<Integer> ids = stackToId.get(id);
         if (ids != null) set.addAll(ids);
         ids = stackToId.get(id | ((stack.getItemDamage() + 1) << 16));
         if (ids != null) set.addAll(ids);
-
         Integer[] tmp = set.toArray(new Integer[set.size()]);
         int[] ret = new int[tmp.length];
         for (int x = 0; x < tmp.length; x++)
