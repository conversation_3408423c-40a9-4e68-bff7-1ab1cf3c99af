--- ../src-base/minecraft/net/minecraftforge/event/ForgeEventFactory.java
+++ ../src-work/minecraft/net/minecraftforge/event/ForgeEventFactory.java
@@ -1,10 +1,13 @@
 package net.minecraftforge.event;
 
 import java.io.File;
+
+import io.github.crucible.CrucibleConfigs;
+import net.minecraft.util.ChatComponentText;
+import net.minecraft.item.Item;
 import java.util.ArrayList;
-import java.util.Iterator;
 import java.util.List;
-
+import net.minecraftforge.cauldron.command.*;
 import cpw.mods.fml.common.ObfuscationReflectionHelper;
 import cpw.mods.fml.common.eventhandler.Event.Result;
 import net.minecraft.block.Block;
@@ -16,9 +19,8 @@
 import net.minecraft.entity.monster.EntityZombie;
 import net.minecraft.entity.player.EntityPlayer;
 import net.minecraft.entity.player.EntityPlayerMP;
-import net.minecraft.init.Blocks;
 import net.minecraft.item.ItemStack;
-import net.minecraft.tileentity.TileEntityBrewingStand;
+import net.minecraft.server.MinecraftServer;
 import net.minecraft.world.Explosion;
 import net.minecraft.world.World;
 import net.minecraft.world.WorldServer;
@@ -28,9 +30,9 @@
 import net.minecraft.world.storage.SaveHandler;
 import net.minecraftforge.common.MinecraftForge;
 import net.minecraftforge.common.util.BlockSnapshot;
+import net.minecraftforge.common.util.FakePlayer;
 import net.minecraftforge.common.util.ForgeDirection;
 import net.minecraftforge.event.brewing.PotionBrewEvent;
-import net.minecraftforge.event.brewing.PotionBrewedEvent;
 import net.minecraftforge.event.entity.EntityStruckByLightningEvent;
 import net.minecraftforge.event.entity.living.LivingHealEvent;
 import net.minecraftforge.event.entity.living.LivingPackSizeEvent;
@@ -48,7 +50,9 @@
 import net.minecraftforge.event.world.BlockEvent.PlaceEvent;
 import net.minecraftforge.event.world.ExplosionEvent;
 import net.minecraftforge.event.world.WorldEvent;
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
 
+//TODO: Crucible-There are a few hidden and weird cauldron/thermos protections, let's add a config file for those protections!
 public class ForgeEventFactory
 {
 
@@ -70,8 +74,26 @@
         return event;
     }
 
+        public static boolean isSpawn(EntityPlayer ep)
+        {
+                //return MinecraftServer.getServer().cauldronConfig.protectSP.getValue() && ep.worldObj.worldInfo.getWorldName().toLowerCase().contains("spawn");
+            String playerWorld = ep.worldObj.worldInfo.getWorldName();
+            for (String world : CrucibleConfigs.configs.crucible_protectedWorld) {
+                if (world.equalsIgnoreCase(playerWorld))
+                    return true;
+            }
+            return false;
+        }
+
     public static boolean doPlayerHarvestCheck(EntityPlayer player, Block block, boolean success)
     {
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        if (CauldronCommand.debug)
+                                System.out.println("Told player no harvest");
+                        return false;
+                }
+
         PlayerEvent.HarvestCheck event = new PlayerEvent.HarvestCheck(player, block, success);
         MinecraftForge.EVENT_BUS.post(event);
         return event.success;
@@ -80,27 +102,224 @@
     @Deprecated // Location version below
     public static float getBreakSpeed(EntityPlayer player, Block block, int metadata, float original)
     {
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        if (CauldronCommand.debug)
+                                System.out.println("Told user breakspeed = 0");
+                        return 0F;
+                }
+
         return getBreakSpeed(player, block, metadata, original, 0, -1, 0);
     }
 
     public static float getBreakSpeed(EntityPlayer player, Block block, int metadata, float original, int x, int y, int z)
     {
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        if (CauldronCommand.debug)
+                                System.out.println("Told user breakspeed = 0");
+                        return 0F;
+                }
+
         PlayerEvent.BreakSpeed event = new PlayerEvent.BreakSpeed(player, block, metadata, original, x, y, z);
         return (MinecraftForge.EVENT_BUS.post(event) ? -1 : event.newSpeed);
     }
 
+        private static boolean outsideOfVanillaRange(int id)
+        {
+                if (CauldronCommand.debug)
+                {
+                        System.out.println("Testing item ID " + id);
+                }
+                return (id > 197 && !(id >= 256 && id <= 431) && !(id >= 2256 && id <= 2267))
+                                && !( id == 628 || id == 5374 || id == 4568 || id == 4354 || id == 4355 || id == 4356
+                                || (id >= 4324 && id <= 4329) || id == 4338 || id == 4688 || id == 4916 || (id <= 4987 && id>= 4980)
+                                || id == 5386 || id == 753 || id == 5497 || id == 4096 || id == 550 || id == 4791 || id == 4790
+                                || id == 564 || id == 562 || id == 555 || id == 4787 || id == 569 || id == 572 || id == 4749 || id == 4984
+                                || id == 4568 || id == 220 || (id >= 4548 && id <= 4561) || (id >= 5360 && id <= 5365));
+        }
+
+
+        public static boolean isOp(EntityPlayer ep)
+        {
+                 return MinecraftServer.getServer().getConfigurationManager().func_152596_g(ep.getGameProfile());
+        }
+        
+        public static boolean isItemIdAllowed(int itemId) {
+                return CrucibleConfigs.configs.crucible_protectedWorldWhitelistInvert ^ CrucibleConfigs.configs.crucible_protectedWorldWhitelist.contains(itemId);
+        }
+
+        public static boolean nonVanilla(EntityPlayer ep)
+        {
+                if (ep == null)
+                {
+                if (CauldronCommand.debug)System.out.println("EntityPlayer is null");
+                        return false;
+                }
+                if (isOp(ep)) return false;
+                if (ep.getHeldItem() == null)
+                {
+                        if (CauldronCommand.debug)
+                                System.out.println("Held item is null");
+                        return false;
+                }
+                if (ep.getHeldItem().getItem() == null)
+                {
+                if (CauldronCommand.debug)System.out.println("Item getItem() is null");
+                        return false;
+                }
+                int itemId = Item.getIdFromItem(ep.getHeldItem().getItem());
+                if (outsideOfVanillaRange(itemId) && !isItemIdAllowed(itemId)) {
+                        if(ep instanceof EntityPlayerMP) {
+                                EntityPlayerMP mp = (EntityPlayerMP)ep;
+                                mp.addChatComponentMessage(new ChatComponentText("You cannot use that item here."));
+                        }
+                        return true;
+                }
+                return false;
+        }
+
     @Deprecated
     public static PlayerInteractEvent onPlayerInteract(EntityPlayer player, Action action, int x, int y, int z, int face)
     {
-        return onPlayerInteract(player, action, x, y, z, face, null);
+    	if(player == null)
+        {
+            	return onPlayerBukkitInteract( player, action, x, y, z, face, null);
+        }
+        org.bukkit.event.block.Action aktor = null;
+        switch (action)
+        {
+            case RIGHT_CLICK_AIR:
+                aktor = org.bukkit.event.block.Action.RIGHT_CLICK_AIR;
+                break;
+            case RIGHT_CLICK_BLOCK:
+                aktor = org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK;
+                break;
+            case LEFT_CLICK_BLOCK:
+                aktor = org.bukkit.event.block.Action.LEFT_CLICK_BLOCK;
+            default:
+                aktor = org.bukkit.event.block.Action.LEFT_CLICK_AIR;
+                break;
+        }
+        return onPlayerBukkitInteract( player, action, x, y, z, face, CraftEventFactory.callPlayerInteractEvent(player, aktor, x, y, z, face, player.inventory.getCurrentItem()));
     }
+
+    @Deprecated
+    public static PlayerInteractEvent onPlayerBukkitInteract(EntityPlayer player, Action action, int x, int y, int z, int face, org.bukkit.event.player.PlayerInteractEvent eve)
+    {
+                PlayerInteractEvent event = new PlayerInteractEvent(player, action, x, y, z, face, null);
+                
+                //if(eve != null && !(player == null || isOp(player) || isCoFHFakePlayer(player,x,y,z,face)))
+                 if(eve != null && !(player == null || isCoFHFakePlayer(player,x,y,z,face))) //Crucible fix op skip.
+                {
+                	event.cb = eve;
+                	if(eve.isCancelled())
+                	{
+                		event.setCanceled(true);
+                		return event;
+                	}
+                }
+                
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event.setCanceled(true);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onPlayerInteract()");
+                } else
+                        MinecraftForge.EVENT_BUS.post(event);
+                return event;
+
+    }
+    
     public static PlayerInteractEvent onPlayerInteract(EntityPlayer player, Action action, int x, int y, int z, int face, World world)
     {
-        PlayerInteractEvent event = new PlayerInteractEvent(player, action, x, y, z, face, world);
-        MinecraftForge.EVENT_BUS.post(event);
-        return event;
+    	//if(player == null || isOp(player) || isCoFHFakePlayer(player,x,y,z,face)) //Crucible fix op skip
+        if(player == null || isCoFHFakePlayer(player,x,y,z,face)) //Crucible fix op skip
+    	{
+        	if(CauldronCommand.debug)
+        	{
+        		System.out.println("[Thermos] CoFH Fake Player / Null player detected...refusing to create Bukkit event");
+        	}
+        	onPlayerBukkitInteract( player, action, x, y, z, face, world, null);
+        }
+    	else
+    	{
+    		if(CauldronCommand.debug)
+    		{
+        		System.out.println("[Thermos] Creating & calling bukkit event!");
+    		}
+    	}
+        org.bukkit.event.block.Action aktor = null;
+        switch (action)
+        {
+            case RIGHT_CLICK_AIR:
+                aktor = org.bukkit.event.block.Action.RIGHT_CLICK_AIR;
+                break;
+            case RIGHT_CLICK_BLOCK:
+                aktor = org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK;
+                break;
+            case LEFT_CLICK_BLOCK:
+                aktor = org.bukkit.event.block.Action.LEFT_CLICK_BLOCK;
+            default:
+                aktor = org.bukkit.event.block.Action.LEFT_CLICK_AIR;
+                break;
+        }    	
+    	return onPlayerBukkitInteract( player, action, x, y, z, face, world, CraftEventFactory.callPlayerInteractEvent(player, aktor, x, y, z, face, player.inventory.getCurrentItem()));
     }
+    
+    private static boolean isCoFHFakePlayer(EntityPlayer player, int x, int y, int z, int face)
+    {
+    	if(CauldronCommand.debug)
+    	{
+    		System.out.println("[Thermos] Checking if it is a CoFH Fake Player...");
+    	}
+    	return player instanceof FakePlayer && player.getGameProfile().getName().equals("[CoFH]");
+    }
+    
+    public static PlayerInteractEvent onPlayerBukkitInteract(EntityPlayer player, Action action, int x, int y, int z, int face, World world, org.bukkit.event.player.PlayerInteractEvent eve)
+    {
+                PlayerInteractEvent event = new PlayerInteractEvent(player, action, x, y, z, face, world);
+                
+                //if(eve != null && !(player == null || isOp(player) || isCoFHFakePlayer(player,x,y,z,face)))
+                if(eve != null && !(player == null || isCoFHFakePlayer(player,x,y,z,face))) //Crucible fix op skip
+                {
+                	if(CauldronCommand.debug)
+                	{
+                		System.out.println("[Thermos] Using Bukkit Event for this action...");
+                	}
+                	event.cb = eve;
+                	if(eve.isCancelled()) 
+                	{ 
+                		event.setCanceled(true); 
+                		return event; 
+                	}
+                }
+                else if(eve == null)
+                {
+                	if(CauldronCommand.debug)
+                	{
+                		System.out.println("[Thermos] Refusing to call Bukkit event for that interaction");
+                	}
+                }
+                else
+                {
+                	if(CauldronCommand.debug)
+                	{
+                		System.out.println("[Thermos] Bukkit event was never considered...single tear");
+                	}                	
+                }
+                
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event.setCanceled(true);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onPlayerInteract()");
+                } else
+                        MinecraftForge.EVENT_BUS.post(event);
+                return event;
 
+    }
+
     public static void onPlayerDestroyItem(EntityPlayer player, ItemStack stack)
     {
         MinecraftForge.EVENT_BUS.post(new PlayerDestroyItemEvent(player, stack));
@@ -182,30 +401,64 @@
         return MinecraftForge.EVENT_BUS.post(new EntityStruckByLightningEvent(entity, bolt));
     }
 
-    public static int onItemUseStart(EntityPlayer player, ItemStack item, int duration)
-    {
-        PlayerUseItemEvent event = new PlayerUseItemEvent.Start(player, item, duration);
-        return MinecraftForge.EVENT_BUS.post(event) ? -1 : event.duration;
-    }
+        public static int onItemUseStart(EntityPlayer player, ItemStack item, int duration)
+        {
+                PlayerUseItemEvent event;
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event = new PlayerUseItemEvent.Start(player, null, duration);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onItemuseStart()");
+                } else
+                {
+                        event = new PlayerUseItemEvent.Start(player, item, duration);
+                }
+                return MinecraftForge.EVENT_BUS.post(event) ? -1 : event.duration;
+        }
 
-    public static int onItemUseTick(EntityPlayer player, ItemStack item, int duration)
-    {
-        PlayerUseItemEvent event = new PlayerUseItemEvent.Tick(player, item, duration);
-        return MinecraftForge.EVENT_BUS.post(event) ? -1 : event.duration;
-    }
+        public static int onItemUseTick(EntityPlayer player, ItemStack item, int duration)
+        {
+                PlayerUseItemEvent event;
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event = new PlayerUseItemEvent.Tick(player, null, duration);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onItemuseTick()");
+                } else
+                {
+                        event = new PlayerUseItemEvent.Tick(player, item, duration);
+                }
+                return MinecraftForge.EVENT_BUS.post(event) ? -1 : event.duration;
+        }
+        public static boolean onUseItemStop(EntityPlayer player, ItemStack item, int duration)
+        {
+                PlayerUseItemEvent event;
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event = new PlayerUseItemEvent.Start(player, null, duration);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onItemuseStop()");
+                } else
+                {
+                        event = new PlayerUseItemEvent.Start(player, item, duration);
+                }
+                return MinecraftForge.EVENT_BUS.post(event);
+        }
 
-    public static boolean onUseItemStop(EntityPlayer player, ItemStack item, int duration)
-    {
-        return MinecraftForge.EVENT_BUS.post(new PlayerUseItemEvent.Stop(player, item, duration));
-    }
+        public static ItemStack onItemUseFinish(EntityPlayer player, ItemStack item, int duration, ItemStack result)
+        {
+                PlayerUseItemEvent.Finish event;
+                if (isSpawn(player) && nonVanilla(player))
+                {
+                        event = new PlayerUseItemEvent.Finish(player, null, duration, null);
+                        if (CauldronCommand.debug)
+                                System.out.println("Canceled onItemuseFinish()");
+                } else
+                        event = new PlayerUseItemEvent.Finish(player, item, duration, result);
+                MinecraftForge.EVENT_BUS.post(event);
+                return event.result;
+        }
 
-    public static ItemStack onItemUseFinish(EntityPlayer player, ItemStack item, int duration, ItemStack result)
-    {
-        PlayerUseItemEvent.Finish event = new PlayerUseItemEvent.Finish(player, item, duration, result);
-        MinecraftForge.EVENT_BUS.post(event);
-        return event.result;
-    }
-
     public static void onStartEntityTracking(Entity entity, EntityPlayer player)
     {
         MinecraftForge.EVENT_BUS.post(new PlayerEvent.StartTracking(player, entity));
