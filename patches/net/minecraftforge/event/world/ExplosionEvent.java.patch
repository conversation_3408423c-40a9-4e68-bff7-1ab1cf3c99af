--- ../src-base/minecraft/net/minecraftforge/event/world/ExplosionEvent.java
+++ ../src-work/minecraft/net/minecraftforge/event/world/ExplosionEvent.java
@@ -2,13 +2,23 @@
 
 import java.util.List;
 
+import org.bukkit.craftbukkit.v1_7_R4.CraftServer;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftEntity;
+import org.bukkit.craftbukkit.v1_7_R4.entity.CraftTNTPrimed;
+import org.bukkit.event.entity.ExplosionPrimeEvent;
 import cpw.mods.fml.common.eventhandler.Cancelable;
 import cpw.mods.fml.common.eventhandler.Event;
 import net.minecraft.entity.Entity;
+import net.minecraft.entity.EntityLivingBase;
+import net.minecraft.entity.item.*;
 import net.minecraft.world.ChunkPosition;
 import net.minecraft.world.Explosion;
-import net.minecraft.world.World;
+import net.minecraft.world.*;
 
+import net.minecraftforge.common.util.*;
+
+import com.mojang.authlib.GameProfile;
+
 /** ExplosionEvent triggers when an explosion happens in the world.<br>
  * <br>
  * ExplosionEvent.Start is fired before the explosion actually occurs.<br>
@@ -23,9 +33,12 @@
 {
     public final World world;
     public final Explosion explosion;
+    public static FakePlayer exploder_fake = null;
+    public static final GameProfile exploder_profile = new GameProfile(null, "[Explosive]");
 
     public ExplosionEvent(World world, Explosion explosion)
     {
+        if(exploder_fake == null || !exploder_fake.worldObj.equals(world)) { exploder_fake = FakePlayerFactory.get( (WorldServer) world, exploder_profile); }
         this.world = world;
         this.explosion = explosion;
     }
@@ -39,10 +52,41 @@
     @Cancelable
     public static class Start extends ExplosionEvent
     {
+        private ExplosionPrimeEvent event;
         public Start(World world, Explosion explosion)
         {
             super(world, explosion);
+            // CraftBukkit start
+            // float f = 4.0F;
+            CraftServer server = world.getServer();
+            CraftEntity ce = null;
+            if(explosion.exploder != null && explosion.exploder instanceof EntityLivingBase)
+            {
+                ce = new CraftTNTPrimed(server, new EntityTNTPrimed(world, explosion.explosionX, explosion.explosionY, explosion.explosionZ, (EntityLivingBase) explosion.exploder ));
+            }
+	    if(ce == null)
+            {
+                ce = new CraftTNTPrimed(server, new EntityTNTPrimed(world, explosion.explosionX, explosion.explosionY, explosion.explosionZ, exploder_fake ));
+            }
+            event = new ExplosionPrimeEvent(ce, 8.0F, true);
+            server.getPluginManager().callEvent(event);
         }
+        @Override
+        public boolean isCanceled()
+        {
+	Entity p_72885_1_ = explosion.exploder;
+            return super.isCanceled() || this.event.isCancelled();
+        }
+        @Override
+        public void setCanceled(boolean cancel)
+        {
+            if (!isCancelable())
+            {
+                throw new IllegalArgumentException("Attempted to cancel a uncancelable event");
+            }
+            super.setCanceled(cancel); this.event.setCancelled(cancel);
+        }
+
     }
 
     /** ExplosionEvent.Detonate is fired once the explosion has a list of affected blocks and entities.  These lists can be modified to change the outcome.<br>
