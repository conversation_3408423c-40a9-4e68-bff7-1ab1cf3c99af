--- ../src-base/minecraft/net/minecraftforge/event/world/BlockEvent.java
+++ ../src-work/minecraft/net/minecraftforge/event/world/BlockEvent.java
@@ -8,14 +8,17 @@
 import cpw.mods.fml.common.eventhandler.Cancelable;
 import cpw.mods.fml.common.eventhandler.Event;
 import net.minecraft.block.Block;
-import net.minecraft.enchantment.EnchantmentHelper;
 import net.minecraft.entity.player.EntityPlayer;
-import net.minecraft.init.Blocks;
 import net.minecraft.item.ItemStack;
 import net.minecraft.world.World;
-import net.minecraftforge.common.ForgeHooks;
 import net.minecraftforge.common.util.BlockSnapshot;
 
+// Cauldron start
+import org.bukkit.craftbukkit.v1_7_R4.event.CraftEventFactory;
+import net.minecraft.entity.player.EntityPlayerMP;
+import org.bukkit.craftbukkit.v1_7_R4.block.CraftBlockState;
+// Cauldron end
+
 public class BlockEvent extends Event {
     private static final boolean DEBUG = Boolean.parseBoolean(System.getProperty("forge.debugBlockEvent", "false"));
 
@@ -80,17 +83,18 @@
             super(x, y, z, world, block, blockMetadata);
             this.player = player;
 
-            if (block == null || !ForgeHooks.canHarvestBlock(block, player, blockMetadata) || // Handle empty block or player unable to break block scenario
-                block.canSilkHarvest(world, player, x, y, z, blockMetadata) && EnchantmentHelper.getSilkTouchModifier(player)) // If the block is being silk harvested, the exp dropped is 0
+            // Cauldron start - handle event on bukkit side
+            org.bukkit.event.block.BlockBreakEvent bukkitEvent = CraftEventFactory.callBlockBreakEvent(world, x, y, z, block, blockMetadata,
+                    (EntityPlayerMP) player);
+            if (bukkitEvent.isCancelled())
             {
-                this.exp = 0;
+                this.setCanceled(true);
             }
             else
             {
-                int meta = block.getDamageValue(world, x, y, z);
-                int bonusLevel = EnchantmentHelper.getFortuneModifier(player);
-                this.exp = block.getExpDrop(world, meta, bonusLevel);
+                this.exp = bukkitEvent.getExpToDrop();
             }
+            // Cauldron end
         }
 
         public EntityPlayer getPlayer()
@@ -140,6 +144,16 @@
             this.blockSnapshot = blockSnapshot;
             this.placedBlock = blockSnapshot.getCurrentBlock();
             this.placedAgainst = placedAgainst;
+            // Cauldron start - handle event on bukkit side
+            CraftBlockState blockstate = CraftBlockState.getBlockState(super.world, super.x, super.y,
+                    super.z);
+            org.bukkit.event.block.BlockPlaceEvent bukkitEvent = CraftEventFactory.callBlockPlaceEvent(super.world, player, blockstate, super.x, super.y,
+                    super.z);
+            if (bukkitEvent.isCancelled() || !bukkitEvent.canBuild())
+            {
+                this.setCanceled(true);
+            }
+            // Cauldron end
             if (DEBUG)
             {
                 System.out.printf("Created PlaceEvent - [PlacedBlock: %s ][PlacedAgainst: %s ][ItemStack: %s ][Player: %s ]\n", placedBlock, placedAgainst, player.getCurrentEquippedItem(), player);
