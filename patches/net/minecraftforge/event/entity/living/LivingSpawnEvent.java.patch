--- ../src-base/minecraft/net/minecraftforge/event/entity/living/LivingSpawnEvent.java
+++ ../src-work/minecraft/net/minecraftforge/event/entity/living/LivingSpawnEvent.java
@@ -48,6 +48,7 @@
         public CheckSpawn(EntityLiving entity, World world, float x, float y, float z)
         {
             super(entity, world, x, y, z);
+            entity.spawnReason = "natural"; // Cauldron - used to handle CraftBukkit's SpawnReason with CustomSpawners
         }
     }
 
@@ -71,6 +72,7 @@
         public SpecialSpawn(EntityLiving entity, World world, float x, float y, float z)
         {
             super(entity, world, x, y, z);
+            entity.spawnReason = "spawner"; // C<PERSON><PERSON> - used to handle CraftBukkit's SpawnReason with CustomSpawners
         }
     }
     
