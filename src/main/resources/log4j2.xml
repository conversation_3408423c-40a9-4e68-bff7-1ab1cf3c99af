<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" packages="net.minecraft,com.mojang">
    <Appenders>
        <Console name="FmlSysOut" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %msg%n"/>
        </Console>
        <Console name="SysOut" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %msg%n"/>
        </Console>
        <Queue name="TerminalConsole">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %msg%n"/>
        </Queue>
        <RollingRandomAccessFile name="File" fileName="logs/latest.log" filePattern="logs/%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level]: %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <OnStartupTriggeringPolicy/>
            </Policies>
        </RollingRandomAccessFile>
        <Routing name="FmlFile">
            <Routes pattern="$${ctx:side}">
                <Route>
                    <RollingRandomAccessFile name="FmlFile" fileName="logs/fml-${ctx:side}-latest.log"
                                             filePattern="logs/fml-${ctx:side}-%i.log">
                        <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger/%X{mod}]: %msg%n"/>
                        <DefaultRolloverStrategy max="3" fileIndex="max"/>
                        <Policies>
                            <OnStartupTriggeringPolicy/>
                        </Policies>
                    </RollingRandomAccessFile>
                </Route>
                <Route key="$${ctx:side}">
                    <RandomAccessFile name="FmlFile" fileName="logs/fml-junk-earlystartup.log">
                        <PatternLayout pattern="[%d{HH:mm:ss}] [%t/%level] [%logger]: %msg%n"/>
                    </RandomAccessFile>
                </Route>
            </Routes>
        </Routing>
    </Appenders>
    <Loggers>
        <!-- Redirect all logging from FML to fml log files -->
        <Logger level="all" name="cpw.mods" additivity="false">
            <AppenderRef ref="FmlFile"/>
        </Logger>
        <Logger level="info" name="com.mojang" additivity="false">
            <AppenderRef ref="TerminalConsole" level="INFO"/>
            <AppenderRef ref="File"/>
        </Logger>
        <Logger level="info" name="net.minecraft" additivity="false">
            <filters>
                <MarkerFilter marker="NETWORK_PACKETS" onMatch="DENY" onMismatch="NEUTRAL"/>
            </filters>
            <AppenderRef ref="TerminalConsole" level="INFO"/>
            <AppenderRef ref="File"/>
        </Logger>
        <Root level="all">
            <AppenderRef ref="FmlSysOut" level="INFO" />
            <AppenderRef ref="TerminalConsole" level="INFO"/>
            <AppenderRef ref="File" level="INFO"/>
            <!-- Set FmlFile to TRACE/DEBUG if you require more logging -->
            <AppenderRef ref="FmlFile" level="INFO"/>
        </Root>
    </Loggers>
</Configuration>
