package org.bukkit.craftbukkit.v1_7_R4.entity;

import org.apache.commons.lang.Validate;
import org.bukkit.craftbukkit.v1_7_R4.CraftServer;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Ocelot;

public class CraftOcelot extends CraftTameableAnimal implements Ocelot {
    public CraftOcelot(CraftServer server, net.minecraft.entity.passive.EntityOcelot wolf) {
        super(server, wolf);
    }

    @Override
    public net.minecraft.entity.passive.EntityOcelot getHandle() {
        return (net.minecraft.entity.passive.EntityOcelot) entity;
    }

    public Type getCatType() {
        return Type.getType(getHandle().getTameSkin());
    }

    public void setCatType(Type type) {
        Validate.notNull(type, "Cat type cannot be null");
        getHandle().setTameSkin(type.getId());
    }

    @Override
    public EntityType getType() {
        return EntityType.OCELOT;
    }
}
