package org.bukkit.craftbukkit.v1_7_R4.entity;

import net.minecraft.entity.item.EntityEnderCrystal;
import org.bukkit.craftbukkit.v1_7_R4.CraftServer;
import org.bukkit.entity.EnderCrystal;
import org.bukkit.entity.EntityType;

public class Craft<PERSON>nder<PERSON>ry<PERSON> extends CraftEntity implements EnderCrystal {
    public CraftEnderCrystal(CraftServer server, EntityEnderCrystal entity) {
        super(server, entity);
    }

    @Override
    public EntityEnderCrystal getHandle() {
        return (EntityEnderCrystal) entity;
    }

    @Override
    public String toString() {
        return "CraftEnderCrystal";
    }

    public EntityType getType() {
        return EntityType.ENDER_CRYSTAL;
    }
}
