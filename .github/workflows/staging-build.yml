name: Pre-release build

on:
  push:
    branches:
      - staging

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: Set up JDK 8
        uses: actions/setup-java@v2
        with:
          java-version: '8'
          distribution: 'temurin'

      - name: Show debug info
        run: ./gradlew --version

      - name: Setup Crucible
        run: ./gradlew cleanCache clean setupCrucible --refresh-dependencies

      - name: Build Crucible distribution packages
        run: ./gradlew buildPackages

      - name: Get short hash
        id: short_sha
        run: echo "::set-output name=sha::$(expr substr ${{ github.sha }} 1 7)"

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: staging-${{ steps.short_sha.outputs.sha }}
          prerelease: true
          name: Staging branch build ${{ steps.short_sha.outputs.sha }}
          body: |
            This is an automatic pre-release build of commit ${{ github.sha }}
            With this release you can test ahead of time the next Crucible release candidate
          files: |
            build/distributions/*
