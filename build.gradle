buildscript {
    repositories {
        mavenCentral()
        //mavenLocal() // for testing plugin pre release
        maven {
            name = "juanmuscaria"
            url = "https://github.com/juanmuscaria/maven/raw/master"
        }
        maven {
            name 'forge'
            url 'https://maven.minecraftforge.net/'
        }
    }
    dependencies {
        classpath 'com.anatawa12.forge:ForgeGradle:1.2-1.1.0'
    }
}

// Base lifecycle tasks are provided by this plugin
apply plugin: 'base'

// Crucible tasks and subproject management is provided by this plugin
apply plugin: 'crucible'

// Needed for jitpack
apply plugin: 'maven-publish'

repositories {
    maven {
        name = "juanmuscaria"
        url = "https://github.com/juanmuscaria/maven/raw/master"
    }
    maven {
        name 'thermos'
        url 'https://github.com/juanmuscaria/maven/raw/master/ThermosLibs'
    }
    maven {
        name 'forge'
        url 'https://maven.minecraftforge.net/'
    }
    maven {
        name 'minecraft'
        url 'https://libraries.minecraft.net/'
    }
    maven {
        name 'gtnh'
        url 'https://nexus.gtnewhorizons.com/repository/public/'
    }
}

minecraft {
    version = '1.7.10'
    mcpVersion = '9.08'
    mainClass = 'cpw.mods.fml.relauncher.ServerLaunchWrapper'
    tweakClass = 'cpw.mods.fml.common.launcher.FMLTweaker'
    installerVersion = "1.4"

    // Repos used on the generated subprojects
    repos = [
            'https://github.com/juanmuscaria/maven/raw/master/ThermosLibs',
            'https://github.com/juanmuscaria/maven/raw/master',
            'https://maven.minecraftforge.net/',
            'https://oss.sonatype.org/content/repositories/snapshots/',
            'https://nexus.gtnewhorizons.com/repository/public/',
            'https://libraries.minecraft.net/'
    ]
}

group = 'io.github.cruciblemc'

ext.buildInfoCached = null
ext.gitInfoCached = null
ext.mcVersion = "1.7.10"
ext.forgeVersion = "1614"
ext.revision = "1.0.7"
var crucibleVersion = "5.4" // TODO: Not hardcode the version.
if (gitInfo('branch') != "master") {
    version = "${mcVersion}-${gitInfo('branch')}-${gitInfo('hash')}"
} else {
    version = "${mcVersion}-${crucibleVersion}"
}

configurations {
    libraries
}

dependencies {
    // New java stuff
    libraries('com.gtnewhorizons.retrofuturabootstrap:RetroFuturaBootstrap:1.0.10') {
        exclude group: 'org.apache.logging.log4j'
    }
    libraries 'org.apache.commons:commons-lang3:3.12.0'
    libraries 'org.apache.commons:commons-compress:1.21'
    libraries 'org.ow2.asm:asm:9.7.1'
    libraries 'org.ow2.asm:asm-commons:9.7.1'
    libraries 'org.ow2.asm:asm-tree:9.7.1'
    libraries 'org.ow2.asm:asm-analysis:9.7.1'
    libraries 'org.ow2.asm:asm-util:9.7.1'
    libraries 'org.ow2.asm:asm-deprecated:7.1'
    libraries 'jakarta.xml.bind:jakarta.xml.bind-api:3.0.1'
    libraries "javax.servlet:javax.servlet-api:4.0.1"
    libraries 'com.sun.xml.bind:jaxb-impl:3.0.2'
    libraries 'org.openjdk.nashorn:nashorn-core:15.4'
    libraries 'it.unimi.dsi:fastutil:8.5.12'

    // Other libs
    libraries 'pw.prok:Damask:0.1.2@jar'
    libraries 'commons-cli:commons-cli:1.3@jar'
    libraries 'org.slf4j:slf4j-simple:1.6.2@jar'
    libraries 'org.eclipse.jetty:jetty-servlet:9.0.3.v20130506@jar'
    libraries 'commons-io:commons-io:2.4@jar'
    libraries 'net.sf.opencsv:opencsv:2.0@jar'
    libraries 'com.beust:jcommander:1.30@jar'
    libraries 'io.github.cruciblemc:launchwrapper:1.13@jar'
    libraries 'com.typesafe.akka:akka-actor_2.11:2.3.3'
    libraries 'com.typesafe:config:1.2.1'
    libraries 'org.scala-lang:scala-actors-migration_2.11:1.1.0'
    libraries 'org.scala-lang:scala-compiler:2.11.7'
    libraries 'org.scala-lang.plugins:scala-continuations-library_2.11:1.0.2'
    libraries 'org.scala-lang.plugins:scala-continuations-plugin_2.11.2:1.0.2'
    libraries 'org.scala-lang:scala-library:2.11.7'
    libraries 'org.scala-lang:scala-parser-combinators:2.11.0-M4'
    libraries 'org.scala-lang:scala-reflect:2.11.7'
    libraries 'org.scala-lang:scala-swing:2.11.0-M7'
    libraries 'org.scala-lang:scala-xml:2.11.0-M4'
    libraries 'net.sf.jopt-simple:jopt-simple:5.0.1'
    libraries 'lzma:lzma:0.0.1'
    libraries 'org.yaml:snakeyaml:1.9'
    libraries 'commons-lang:commons-lang:2.6'
    libraries 'org.avaje:ebean:2.7.3'
    libraries 'jline:jline:2.6'
    libraries 'net.md-5:SpecialSource:1.10.0'
    libraries 'net.sourceforge.argo:argo:2.25'
    libraries 'com.googlecode.json-simple:json-simple:1.1'
    libraries 'org.xerial:sqlite-jdbc:3.7.2'
    libraries 'mysql:mysql-connector-java:5.1.14'
    libraries 'javax.persistence:persistence-api:1.0.2'
    libraries 'pw.prok:KImagine:0.2.0@jar'
    libraries 'org.apache.httpcomponents:httpclient:4.4.1'
    libraries 'net.openhft:affinity:3.0.1'
    libraries 'org.fusesource.jansi:jansi:1.11'
    libraries 'com.koloboke:koloboke-impl-jdk8:1.0.0'
    libraries 'java3d:vecmath:1.3.1'
    libraries 'net.minecraft:server:1.7.10'
}

packageApi {
    include 'org/bukkit/**'
    include 'org/spigotmc/**'
    include 'thermos/**'
    include 'co/aikar/**'
    include 'io/github/crucible/**'
    include 'net/minecraftforge/cauldron/**'
    include 'net/md_5/**'
}

tasks.register('packageJavadoc', Jar) {
    dependsOn ':eclipse:cauldron:javadoc'
    archiveClassifier = 'javadoc'
    destinationDirectory = getLibsDirectory()
}

packageServer {
    manifest {
        attributes([
                'Thermos-Git-Branch'    : gitInfo('branch'),
                'Thermos-Git-Hash'      : gitInfo('fullHash'),
                'Thermos-Group'         : project.group,
                'Thermos-Channel'       : project.name,
                'Thermos-Version'       : project.version,
                'Thermos-Legacy'        : true,
                'Implementation-Vendor' : 'CrucibleMC Team',
                'Implementation-Title'  : project.name,
                'Implementation-Version': project.version,
                'Specification-Vendor'  : 'Bukkit Team',
                'Specification-Title'   : 'Bukkit',
                'Specification-Version' : '1.7.10-R0.1-SNAPSHOT',
                'Forge-Version'         : '10.13.4.1614',
                'TweakClass'            : 'cpw.mods.fml.common.launcher.FMLTweaker',
                'Main-Class'            : 'cpw.mods.fml.relauncher.ServerLaunchWrapper',
                'Class-Path'            : generateClasspath(),
                'Crucible-Libs'         : generateMavenLibs()
        ])
    }
    dependsOn("packageLibraries")
}

// TODO: Move this into the gradle plugin, generate md5 as well
tasks.register('packageLibraries', Zip) {
    archiveFileName = 'libraries.zip'
    destinationDirectory = file("$buildDir/distributions")
    outputs.upToDateWhen {
        false // TODO: Not 100% sure how to make this task re-run when libraries are changed...
    }

    configurations.libraries.resolvedConfiguration.resolvedArtifacts.collect {
        def moduleVersion = it.moduleVersion
        from(it.file) {
            into("${moduleVersion.id.group.replace('.', '/')}/${moduleVersion.id.name}/${moduleVersion.id.version}/")
        }
    }

    group('crucible')
    description("Package all necessary libraries to run Crucible, in case the server is unable to download them at runtime")
}

publishing {
    publications {
        crucible(MavenPublication) {
            artifact packageApi
            artifact packageJavadoc
        }
    }
    repositories {
        maven {
            url = buildDir.absolutePath + "/repo"
            name = "filesystem"
        }
    }
}

// TODO: This is absolute awful
//  Move this to the gradle plugin?
var actualProject = project
subprojects {
    if (project.name.contains('cauldron')) {
        // Here we are presented with an annoying situation. We need to configure the javadoc task within the cauldron project.
        // However we can't modify the build gradle directly... Besides this "solution" a better alternative would be the following:
        // * Create a cauldron.gradle file at the root of the project that will be appended over the generated file
        // * Change this directly in the gradle plugin project gen
        // For now I'll leave this quick hack here that will sure haunt me in the future
        afterEvaluate {
            //noinspection ConfigurationAvoidance
            if (project.tasks.findByName("javadoc")) {
                javadoc {
                    include 'org/bukkit/**'
                    include 'org/spigotmc/**'
                    include 'thermos/**'
                    include 'co/aikar/**'
                    include 'io/github/crucible/**'
                    include 'net/minecraftforge/cauldron/**'
                    include 'net/md_5/**'
                }

                var cauldron = project
                actualProject.tasks.named('packageJavadoc') {
                    from cauldron.javadoc.destinationDir
                }
            }
        }
    }
}

def gitInfo(String key) {
    if (!gitInfoCached) {
        if (file('.git').exists()) {
            gitInfoCached = [
                    hash    : ['git', 'log', "--format=%h", '-n', '1'].execute().text.trim(),
                    fullHash: ['git', 'log', "--format=%H", '-n', '1'].execute().text.trim(),
                    branch  : ['git', 'symbolic-ref', '--short', 'HEAD'].execute().text.trim(),
                    message : ['git', 'log', "--format=%B", '-n', '1'].execute().text.trim()
            ]
        } else {
            gitInfoCached = [
                    hash    : 'NOT_A_GIT',
                    fullHash: 'NOT_A_GIT',
                    branch  : 'NOT_A_GIT',
                    message : 'NOT_A_GIT'
            ]
        }
    }
    return key ? gitInfoCached[key] : gitInfoCached
}

def generateClasspath() {
    def classpath = ''
    configurations.libraries.resolvedConfiguration.resolvedArtifacts.collect { it.moduleVersion.id }.each {
        def jar = "${it.group.replace('.', '/')}/${it.name}/${it.version}/${it.name}-${it.version}.jar"
        classpath += " ${'libraries/'}${jar}"
    }
    return classpath.trim()
}

def generateMavenLibs() {
    def libs = ''
    configurations.libraries.resolvedConfiguration.resolvedArtifacts.collect { it.moduleVersion.id }.each {
        libs += " ${it.group}:${it.name}:${it.version}"
    }
    return libs.trim()
}
